const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

test.describe('Server Deployment (Issue #6)', () => {

  const projectRoot = path.resolve(__dirname, '..');
  const testId = 'deploy-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  const monitorsFile = path.join(projectRoot, `monitors-${testId}.json`);
  const logsDir = path.join(projectRoot, `logs-${testId}`);
  const checkScript = path.join(projectRoot, 'check-monitors.php');

  // Helper function to execute PHP script
  const runPhpScript = (args = '') => {
    return new Promise((resolve, reject) => {
      const env = { 
        ...process.env, 
        MONITORS_FILE: monitorsFile,
        LOGS_DIR: logsDir
      };
      exec(`C:\\xampp\\php\\php.exe ${checkScript} ${args}`, { 
        cwd: projectRoot,
        env: env
      }, (error, stdout, stderr) => {
        resolve({ error, stdout, stderr, exitCode: error ? error.code : 0 });
      });
    });
  };

  // Helper function to simulate Linux PHP execution
  const simulateLinuxPhp = (args = '') => {
    return new Promise((resolve, reject) => {
      const env = { 
        ...process.env, 
        MONITORS_FILE: monitorsFile,
        LOGS_DIR: logsDir
      };
      // Simulate Linux path
      const linuxCommand = `php ${checkScript} ${args}`;
      exec(`C:\\xampp\\php\\php.exe ${checkScript} ${args}`, { 
        cwd: projectRoot,
        env: env
      }, (error, stdout, stderr) => {
        resolve({ error, stdout, stderr, exitCode: error ? error.code : 0 });
      });
    });
  };

  // Helper function to clean up test files
  const cleanup = () => {
    if (fs.existsSync(monitorsFile)) {
      fs.unlinkSync(monitorsFile);
    }
    if (fs.existsSync(logsDir)) {
      fs.rmSync(logsDir, { recursive: true, force: true });
    }
  };

  test.beforeEach(() => {
    cleanup();
  });

  test.afterEach(() => {
    cleanup();
  });

  test.describe('Cron Compatibility', () => {

    test('should be runnable from command line with proper exit codes', async () => {
      // Create test monitors
      const testMonitors = [{
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        status: 'active',
        added_at: new Date().toISOString()
      }];
      fs.writeFileSync(monitorsFile, JSON.stringify(testMonitors, null, 2));

      // Run script
      const result = await runPhpScript();
      
      // Should exit with code 0 (success)
      expect(result.exitCode).toBe(0);
      expect(result.error).toBeNull();
    });

    test('should handle cron-style execution (*/5 * * * *)', async () => {
      // Test that script can run every 5 minutes without issues
      const testMonitors = [{
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        status: 'active',
        added_at: new Date().toISOString()
      }];
      fs.writeFileSync(monitorsFile, JSON.stringify(testMonitors, null, 2));

      // Run multiple times to simulate cron
      for (let i = 0; i < 3; i++) {
        const result = await runPhpScript();
        expect(result.exitCode).toBe(0);
        
        // Small delay between runs
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Check that logs were created properly
      expect(fs.existsSync(logsDir)).toBe(true);
      const logFiles = fs.readdirSync(logsDir);
      expect(logFiles.length).toBeGreaterThan(0);
    });

    test('should support command line arguments', async () => {
      // Test --help argument
      const helpResult = await runPhpScript('--help');
      expect(helpResult.exitCode).toBe(0);
      expect(helpResult.stdout).toContain('Usage:');
      expect(helpResult.stdout).toContain('*/5 * * * *');

      // Test --dry-run argument
      const testMonitors = [{
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        status: 'active',
        added_at: new Date().toISOString()
      }];
      fs.writeFileSync(monitorsFile, JSON.stringify(testMonitors, null, 2));

      const dryRunResult = await runPhpScript('--dry-run');
      expect(dryRunResult.exitCode).toBe(0);
      expect(dryRunResult.stdout).toContain('DRY RUN MODE');
      expect(dryRunResult.stdout).toContain('https://httpbin.org/status/200');
    });

    test('should handle errors gracefully with proper exit codes', async () => {
      // Create invalid monitors file
      fs.writeFileSync(monitorsFile, 'invalid json content');

      const result = await runPhpScript();
      
      // Should handle error gracefully
      expect(result.exitCode).toBe(0); // Script should not crash
    });

    test('should work without web server running', async () => {
      // This test ensures the cron script doesn't depend on web server
      const testMonitors = [{
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        status: 'active',
        added_at: new Date().toISOString()
      }];
      fs.writeFileSync(monitorsFile, JSON.stringify(testMonitors, null, 2));

      // Run script (should work independently of web server)
      const result = await runPhpScript();
      expect(result.exitCode).toBe(0);
      
      // Check that monitoring was performed
      const logFiles = fs.readdirSync(logsDir);
      expect(logFiles.length).toBeGreaterThan(0);
    });

  });

  test.describe('Linux Environment Compatibility', () => {

    test('should use relative paths compatible with Linux', async () => {
      // Check that script uses __DIR__ and relative paths
      const scriptContent = fs.readFileSync(checkScript, 'utf8');
      
      // Should use __DIR__ for relative paths
      expect(scriptContent).toContain('__DIR__');
      expect(scriptContent).toContain("require_once __DIR__ . '/config.php'");
      
      // Should not contain Windows-specific paths
      expect(scriptContent).not.toContain('C:\\');
      expect(scriptContent).not.toContain('\\\\');
    });

    test('should work with Linux-style PHP execution', async () => {
      const testMonitors = [{
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        status: 'active',
        added_at: new Date().toISOString()
      }];
      fs.writeFileSync(monitorsFile, JSON.stringify(testMonitors, null, 2));

      // Simulate Linux execution
      const result = await simulateLinuxPhp();
      expect(result.exitCode).toBe(0);
    });

    test('should handle Unix line endings', async () => {
      // Check that script doesn't have Windows-specific line endings issues
      const scriptContent = fs.readFileSync(checkScript, 'utf8');
      
      // Should not have \r\n (Windows) line endings in critical parts
      const lines = scriptContent.split('\n');
      expect(lines.length).toBeGreaterThan(10); // Should have multiple lines
    });

    test('should be compatible with different PHP versions', async () => {
      // Check for PHP version compatibility
      const scriptContent = fs.readFileSync(checkScript, 'utf8');
      
      // Should use compatible PHP syntax
      expect(scriptContent).toContain('<?php');
      expect(scriptContent).not.toContain('<?='); // Avoid short tags
      
      // Should not use very new PHP features that might not be available
      expect(scriptContent).not.toContain('match('); // PHP 8.0+
      expect(scriptContent).not.toContain('?->'); // PHP 8.0+
    });

  });

  test.describe('File Permissions and Security', () => {

    test('should create directories with proper permissions', async () => {
      const testMonitors = [{
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        status: 'active',
        added_at: new Date().toISOString()
      }];
      fs.writeFileSync(monitorsFile, JSON.stringify(testMonitors, null, 2));

      // Run script to create logs directory
      const result = await runPhpScript();
      expect(result.exitCode).toBe(0);

      // Check that logs directory was created
      expect(fs.existsSync(logsDir)).toBe(true);
      
      // Check directory stats (permissions)
      const stats = fs.statSync(logsDir);
      expect(stats.isDirectory()).toBe(true);
    });

    test('should handle file writing permissions gracefully', async () => {
      // Create monitors file
      const testMonitors = [{
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        status: 'active',
        added_at: new Date().toISOString()
      }];
      fs.writeFileSync(monitorsFile, JSON.stringify(testMonitors, null, 2));

      // Run script
      const result = await runPhpScript();
      expect(result.exitCode).toBe(0);

      // Check that log files were created
      const logFiles = fs.readdirSync(logsDir);
      expect(logFiles.length).toBeGreaterThan(0);
      
      // Check that log files are readable
      const logFile = path.join(logsDir, logFiles[0]);
      const logContent = fs.readFileSync(logFile, 'utf8');
      expect(logContent.length).toBeGreaterThan(0);
    });

    test('should not expose sensitive information in logs', async () => {
      const testMonitors = [{
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        status: 'active',
        added_at: new Date().toISOString()
      }];
      fs.writeFileSync(monitorsFile, JSON.stringify(testMonitors, null, 2));

      const result = await runPhpScript();
      expect(result.exitCode).toBe(0);

      // Check log content for sensitive information
      const logFiles = fs.readdirSync(logsDir);
      const logFile = path.join(logsDir, logFiles[0]);
      const logContent = fs.readFileSync(logFile, 'utf8');
      
      // Should not contain passwords or sensitive data
      expect(logContent).not.toContain('password');
      expect(logContent).not.toContain('secret');
      expect(logContent).not.toContain('key');
      
      // Should contain monitoring information
      expect(logContent).toContain('Starting monitoring check cycle');
    });

    test('should handle concurrent access safely', async () => {
      const testMonitors = [{
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        status: 'active',
        added_at: new Date().toISOString()
      }];
      fs.writeFileSync(monitorsFile, JSON.stringify(testMonitors, null, 2));

      // Run multiple instances concurrently
      const promises = [];
      for (let i = 0; i < 3; i++) {
        promises.push(runPhpScript());
      }

      const results = await Promise.all(promises);
      
      // All should complete successfully
      results.forEach(result => {
        expect(result.exitCode).toBe(0);
      });

      // Check that logs were created properly
      expect(fs.existsSync(logsDir)).toBe(true);
    });

  });

  test.describe('Web Server Configuration', () => {

    test('should work with Apache configuration', async ({ page }) => {
      // Test that web interface is accessible
      await page.goto('/');
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('form')).toBeVisible();
    });

    test('should handle different document root configurations', async ({ page }) => {
      // Test that application works regardless of document root
      await page.goto('/index.php');
      const h1Text = await page.locator('h1').textContent();
      expect(h1Text).toContain('Uptime Monitor');
      
      // Test direct access to dashboard (should redirect)
      await page.goto('/dashboard.php');
      await expect(page).toHaveURL(/index\.php/);
    });

    test('should serve static assets correctly', async ({ page }) => {
      // Test that CSS is loaded properly
      await page.goto('/');
      
      const styles = await page.evaluate(() => {
        const styleSheets = Array.from(document.styleSheets);
        return styleSheets.length > 0;
      });
      
      // Should have styles (either inline or external)
      expect(styles).toBe(true);
    });

    test('should handle URL rewriting gracefully', async ({ page }) => {
      // Test that application works with and without URL rewriting
      await page.goto('/');
      await expect(page.locator('h1')).toBeVisible();
      
      // Test with explicit .php extension
      await page.goto('/index.php');
      await expect(page.locator('h1')).toBeVisible();
    });

  });

});

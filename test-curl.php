<?php
echo "<h2>PHP cURL Test</h2>";

// Check if cURL is enabled
if (function_exists('curl_init')) {
    echo "✅ cURL is enabled<br>";
    
    // Test Google
    echo "<h3>Testing Google:</h3>";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://google.com');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Test Bot');
    curl_setopt($ch, CURLOPT_NOBODY, true);
    
    $start = microtime(true);
    $result = curl_exec($ch);
    $end = microtime(true);
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $time = round(($end - $start) * 1000);
    
    curl_close($ch);
    
    echo "HTTP Code: $http_code<br>";
    echo "Response Time: {$time}ms<br>";
    echo "Error: " . ($error ? $error : "None") . "<br>";
    
    if ($http_code >= 200 && $http_code < 400) {
        echo "✅ Status: ONLINE<br>";
    } else {
        echo "❌ Status: OFFLINE<br>";
    }
    
} else {
    echo "❌ cURL is NOT enabled<br>";
}

echo "<h3>PHP Info:</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Extensions: " . implode(', ', get_loaded_extensions()) . "<br>";
?>

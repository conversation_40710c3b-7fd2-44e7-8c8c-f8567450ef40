# 🚀 Deployment Guide

This guide covers deploying the Uptime Monitor application to a production server.

## 📋 Prerequisites

- **PHP 7.4+** with the following extensions:
  - `curl` (for HTTP requests)
  - `json` (for data storage)
  - `session` (for authentication)
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **Cron** access for automated monitoring
- **File system** write permissions

## 📁 File Structure

```
uptime-monitor/
├── index.php              # Main login page
├── dashboard.php           # Monitor management interface
├── logout.php             # Logout handler
├── config.php             # Application configuration
├── check-monitors.php     # Cron monitoring script
├── api/                   # API endpoints
│   ├── auth.php
│   └── monitors.php
├── logs/                  # Log files (auto-created)
├── monitors.json          # Monitor storage (auto-created)
└── users.json            # User storage (auto-created)
```

## 🔧 Installation Steps

### 1. Upload Files

Upload all PHP files to your web server's document root or a subdirectory.

```bash
# Example for subdirectory installation
/var/www/html/uptime-monitor/
```

### 2. Set File Permissions

```bash
# Make directories writable by web server
chmod 755 /var/www/html/uptime-monitor/
chmod 755 /var/www/html/uptime-monitor/api/

# Create and set permissions for data directories
mkdir -p /var/www/html/uptime-monitor/logs
chmod 755 /var/www/html/uptime-monitor/logs

# Ensure PHP files are readable
chmod 644 /var/www/html/uptime-monitor/*.php
chmod 644 /var/www/html/uptime-monitor/api/*.php

# Make cron script executable
chmod 755 /var/www/html/uptime-monitor/check-monitors.php
```

### 3. Configure Web Server

#### Apache Configuration

Create `.htaccess` file in the application directory:

```apache
# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Prevent access to sensitive files
<Files "*.json">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files "config.php">
    Require all denied
</Files>

# Optional: Clean URLs (remove .php extension)
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^.]+)$ $1.php [NC,L]
```

#### Nginx Configuration

Add to your Nginx server block:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html/uptime-monitor;
    index index.php;

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # Prevent access to sensitive files
    location ~ \.(json|log)$ {
        deny all;
    }

    location = /config.php {
        deny all;
    }

    # PHP processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Optional: Clean URLs
    location / {
        try_files $uri $uri/ $uri.php?$query_string;
    }
}
```

### 4. Set Up Cron Job

Add the following cron job to run monitoring every 5 minutes:

```bash
# Edit crontab
crontab -e

# Add this line (adjust path as needed)
*/5 * * * * /usr/bin/php /var/www/html/uptime-monitor/check-monitors.php >/dev/null 2>&1
```

#### Cron Job Options

```bash
# Basic monitoring every 5 minutes
*/5 * * * * /usr/bin/php /path/to/check-monitors.php

# With verbose logging
*/5 * * * * /usr/bin/php /path/to/check-monitors.php --verbose >> /var/log/uptime-monitor.log 2>&1

# Test the cron script manually
php /path/to/check-monitors.php --dry-run
php /path/to/check-monitors.php --help
```

## 🔐 Security Considerations

### 1. File Permissions

```bash
# Recommended permissions
find /var/www/html/uptime-monitor -type f -name "*.php" -exec chmod 644 {} \;
find /var/www/html/uptime-monitor -type d -exec chmod 755 {} \;
chmod 600 /var/www/html/uptime-monitor/*.json  # If they exist
```

### 2. Web Server Security

- **Hide sensitive files**: Ensure `.json` and `.log` files are not accessible via web
- **Use HTTPS**: Configure SSL/TLS for production
- **Firewall**: Restrict access to admin interface if needed

### 3. Application Security

- **Change default credentials**: Login with `admin/admin` and change password
- **Regular updates**: Keep PHP and web server updated
- **Monitor logs**: Check application logs regularly

## 🧪 Testing Deployment

### 1. Test Web Interface

```bash
# Test login page
curl -I http://your-domain.com/uptime-monitor/

# Test with credentials
curl -X POST http://your-domain.com/uptime-monitor/ \
  -d "username=admin&password=admin"
```

### 2. Test Cron Script

```bash
# Test script execution
php /path/to/check-monitors.php --dry-run

# Test with verbose output
php /path/to/check-monitors.php --verbose

# Check logs
tail -f /path/to/uptime-monitor/logs/monitor-$(date +%Y-%m-%d).log
```

### 3. Test Monitoring

1. Login to web interface
2. Add a test monitor (e.g., `https://httpbin.org/status/200`)
3. Wait 5 minutes for cron to run
4. Check logs for monitoring activity

## 📊 Monitoring and Maintenance

### Log Files

```bash
# Monitor logs location
/path/to/uptime-monitor/logs/monitor-YYYY-MM-DD.log

# View recent logs
tail -f /path/to/uptime-monitor/logs/monitor-$(date +%Y-%m-%d).log

# Log rotation (optional)
logrotate /etc/logrotate.d/uptime-monitor
```

### Backup

```bash
# Backup data files
cp /path/to/uptime-monitor/monitors.json /backup/
cp /path/to/uptime-monitor/users.json /backup/
cp -r /path/to/uptime-monitor/logs/ /backup/
```

## 🔧 Troubleshooting

### Common Issues

1. **Cron not running**:
   ```bash
   # Check cron service
   systemctl status cron
   
   # Check cron logs
   grep CRON /var/log/syslog
   ```

2. **Permission errors**:
   ```bash
   # Fix permissions
   chown -R www-data:www-data /path/to/uptime-monitor/
   chmod -R 755 /path/to/uptime-monitor/
   ```

3. **PHP errors**:
   ```bash
   # Check PHP error log
   tail -f /var/log/php_errors.log
   
   # Test PHP syntax
   php -l /path/to/check-monitors.php
   ```

### Debug Mode

```bash
# Run script with verbose output
php /path/to/check-monitors.php --verbose

# Check what would be monitored
php /path/to/check-monitors.php --dry-run
```

## 📈 Performance Optimization

- **Log rotation**: Set up automatic log rotation
- **Monitor cleanup**: Periodically clean old monitors
- **Resource limits**: Monitor PHP memory usage
- **Caching**: Consider adding caching for large monitor lists

## 🆙 Updates

To update the application:

1. Backup current installation
2. Upload new PHP files
3. Test functionality
4. Update cron job if needed

---

For support and issues, check the application logs and ensure all prerequisites are met.

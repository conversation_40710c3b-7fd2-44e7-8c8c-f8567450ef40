const { test, expect } = require('@playwright/test');

test.describe('Minimal User Interface (Issue #5)', () => {

  test.beforeEach(async ({ page }) => {
    // Clear any existing sessions
    try {
      await page.goto('/logout.php');
    } catch (error) {
      // Ignore errors
    }
  });

  test.describe('Plain HTML Structure', () => {

    test('should use semantic HTML elements without complex structure', async ({ page }) => {
      await page.goto('/');
      
      // Check basic HTML structure
      await expect(page.locator('html')).toBeVisible();
      await expect(page.locator('head')).toHaveCount(1);
      await expect(page.locator('body')).toBeVisible();
      
      // Check for semantic elements
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('form')).toBeVisible();
      await expect(page.locator('label').first()).toBeVisible();
      await expect(page.locator('input').first()).toBeVisible();
      
      // Should not use complex layout elements
      const complexElements = await page.locator('nav, aside, section, article, header, footer').count();
      expect(complexElements).toBeLessThanOrEqual(1); // Allow minimal header/footer
    });

    test('should have clear form structure for login', async ({ page }) => {
      await page.goto('/');
      
      // Check login form elements
      await expect(page.locator('form')).toBeVisible();
      await expect(page.locator('input[name="username"]')).toBeVisible();
      await expect(page.locator('input[name="password"]')).toBeVisible();
      await expect(page.locator('input[type="submit"]')).toBeVisible();
      
      // Check labels for accessibility
      await expect(page.locator('label[for="username"]')).toBeVisible();
      await expect(page.locator('label[for="password"]')).toBeVisible();
      
      // Form should be simple and direct
      const formInputs = await page.locator('form input').count();
      expect(formInputs).toBeLessThanOrEqual(3); // username, password, submit
    });

    test('should have clear form structure for URL submission', async ({ page }) => {
      // Login first
      await page.goto('/');
      await page.fill('input[name="username"]', 'admin');
      await page.fill('input[name="password"]', 'admin');
      await page.click('input[type="submit"]');
      
      // Check dashboard form
      await expect(page.locator('form')).toBeVisible();
      await expect(page.locator('input[name="url"]')).toBeVisible();
      await expect(page.locator('input[name="email"]')).toBeVisible();
      await expect(page.locator('input[type="submit"]')).toBeVisible();
      
      // Check labels
      await expect(page.locator('label[for="url"]')).toBeVisible();
      await expect(page.locator('label[for="email"]')).toBeVisible();
      
      // Form should be simple
      const formInputs = await page.locator('form input').count();
      expect(formInputs).toBeLessThanOrEqual(3); // url, email, submit
    });

  });

  test.describe('Minimal CSS Requirements', () => {

    test('should use minimal CSS without advanced styling', async ({ page }) => {
      await page.goto('/');
      
      // Get all style elements and inline styles
      const styleElements = await page.locator('style').count();
      const linkElements = await page.locator('link[rel="stylesheet"]').count();
      
      // Should have minimal external stylesheets
      expect(linkElements).toBeLessThanOrEqual(1);
      
      // Check that CSS is not overly complex
      if (styleElements > 0) {
        const styleContent = await page.locator('style').first().textContent();
        
        // Should not use advanced CSS features
        expect(styleContent).not.toContain('flex');
        expect(styleContent).not.toContain('grid');
        expect(styleContent).not.toContain('transform');
        expect(styleContent).not.toContain('animation');
        expect(styleContent).not.toContain('box-shadow');
        
        // Should not have excessive styling
        const cssRules = styleContent.split('{').length - 1;
        expect(cssRules).toBeLessThan(20); // Minimal CSS rules
      }
    });

    test('should not use complex visual effects', async ({ page }) => {
      await page.goto('/');
      
      // Check for absence of complex visual effects
      const bodyStyles = await page.locator('body').evaluate(el => {
        const styles = window.getComputedStyle(el);
        return {
          boxShadow: styles.boxShadow,
          borderRadius: styles.borderRadius,
          background: styles.background,
          transition: styles.transition
        };
      });
      
      // Should not have complex shadows or effects
      expect(bodyStyles.boxShadow).toBe('none');
      // Transition can be 'all' or 'all 0s ease 0s' depending on browser
      expect(bodyStyles.transition).toMatch(/^(all|all 0s ease 0s)$/);
    });

    test('should maintain readability without fancy styling', async ({ page }) => {
      await page.goto('/');
      
      // Check that text is readable
      const headingText = await page.locator('h1').textContent();
      expect(headingText).toBeTruthy();
      expect(headingText.length).toBeGreaterThan(0);
      
      // Check form labels are clear
      const usernameLabel = await page.locator('label[for="username"]').textContent();
      const passwordLabel = await page.locator('label[for="password"]').textContent();
      
      expect(usernameLabel).toContain('Username');
      expect(passwordLabel).toContain('Password');
      
      // Text should be visible and readable
      const labelColor = await page.locator('label').first().evaluate(el => {
        return window.getComputedStyle(el).color;
      });
      expect(labelColor).not.toBe('rgb(255, 255, 255)'); // Not white on white
    });

  });

  test.describe('No JavaScript Requirements', () => {

    test('should function without JavaScript enabled', async ({ browser }) => {
      // Create context with JavaScript disabled
      const context = await browser.newContext({
        javaScriptEnabled: false
      });
      const page = await context.newPage();
      
      await page.goto('/');
      
      // Should still show login form
      await expect(page.locator('form')).toBeVisible();
      await expect(page.locator('input[name="username"]')).toBeVisible();
      await expect(page.locator('input[name="password"]')).toBeVisible();
      
      // Form should be submittable (basic HTML form)
      await page.fill('input[name="username"]', 'admin');
      await page.fill('input[name="password"]', 'admin');
      
      // Submit form (this should work without JS)
      await page.click('input[type="submit"]');
      
      // Should redirect to dashboard (server-side processing)
      await expect(page).toHaveURL(/dashboard\.php/);
      
      await context.close();
    });

    test('should not include unnecessary JavaScript', async ({ page }) => {
      await page.goto('/');
      
      // Check for script tags
      const scriptTags = await page.locator('script').count();
      
      // Should have minimal or no JavaScript
      expect(scriptTags).toBeLessThanOrEqual(1); // Allow one small script if essential
      
      if (scriptTags > 0) {
        const scriptContent = await page.locator('script').first().textContent();
        
        // If there is JavaScript, it should be minimal
        expect(scriptContent.length).toBeLessThan(500); // Very small scripts only
        
        // Should not use complex JavaScript features
        expect(scriptContent).not.toContain('addEventListener');
        expect(scriptContent).not.toContain('fetch');
        expect(scriptContent).not.toContain('XMLHttpRequest');
        expect(scriptContent).not.toContain('jQuery');
      }
    });

    test('should work with basic HTML form submission', async ({ page }) => {
      await page.goto('/');
      
      // Check form method and action
      const formMethod = await page.locator('form').getAttribute('method');
      const formAction = await page.locator('form').getAttribute('action');
      
      expect(formMethod?.toLowerCase()).toBe('post');
      // Action can be empty string (submits to same page) or have value
      expect(formAction).toBeDefined();
      
      // Form should use standard HTML input types
      const usernameType = await page.locator('input[name="username"]').getAttribute('type');
      const passwordType = await page.locator('input[name="password"]').getAttribute('type');
      
      expect(usernameType).toBe('text');
      expect(passwordType).toBe('password');
    });

  });

  test.describe('Clarity and Usability', () => {

    test('should have clear page titles and headings', async ({ page }) => {
      await page.goto('/');
      
      // Check page title
      const title = await page.title();
      expect(title).toContain('Uptime Monitor');
      expect(title).toContain('Login');
      
      // Check main heading
      const heading = await page.locator('h1').textContent();
      expect(heading).toContain('Uptime Monitor');
      
      // Login to check dashboard
      await page.fill('input[name="username"]', 'admin');
      await page.fill('input[name="password"]', 'admin');
      await page.click('input[type="submit"]');
      
      // Check dashboard title and heading
      const dashboardTitle = await page.title();
      expect(dashboardTitle).toContain('Dashboard');
      
      const dashboardHeading = await page.locator('h1, h2').first().textContent();
      expect(dashboardHeading).toBeTruthy();
    });

    test('should provide clear instructions and feedback', async ({ page }) => {
      await page.goto('/');
      
      // Should have clear input placeholders or instructions
      const urlInput = page.locator('input[name="username"]');
      const placeholder = await urlInput.getAttribute('placeholder');
      
      // Login and check dashboard
      await page.fill('input[name="username"]', 'admin');
      await page.fill('input[name="password"]', 'admin');
      await page.click('input[type="submit"]');
      
      // Check dashboard form has clear labels
      const urlLabel = await page.locator('label[for="url"]').textContent();
      const emailLabel = await page.locator('label[for="email"]').textContent();
      
      expect(urlLabel).toContain('URL');
      expect(emailLabel).toContain('email');
    });

    test('should show error messages clearly', async ({ page }) => {
      await page.goto('/');
      
      // Try invalid login
      await page.fill('input[name="username"]', 'invalid');
      await page.fill('input[name="password"]', 'invalid');
      await page.click('input[type="submit"]');
      
      // Should show error message
      const errorMessage = page.locator('.error, [class*="error"]');
      await expect(errorMessage).toBeVisible();
      
      const errorText = await errorMessage.textContent();
      expect(errorText).toContain('Invalid');
    });

    test('should have logical navigation flow', async ({ page }) => {
      await page.goto('/');
      
      // Should start at login
      await expect(page.locator('input[name="username"]')).toBeVisible();
      
      // Login should lead to dashboard
      await page.fill('input[name="username"]', 'admin');
      await page.fill('input[name="password"]', 'admin');
      await page.click('input[type="submit"]');
      
      await expect(page).toHaveURL(/dashboard\.php/);
      
      // Dashboard should have logout option
      const logoutLink = page.locator('a[href*="logout"]');
      await expect(logoutLink).toBeVisible();
      
      // Logout should return to login
      await logoutLink.click();
      await expect(page).toHaveURL(/index\.php/);
    });

  });

});

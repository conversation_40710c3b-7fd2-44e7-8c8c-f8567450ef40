const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

test.describe('SQLite Database System', () => {

  test.beforeEach(async ({ page }) => {
    // Clear any existing sessions
    try {
      await page.goto('/logout.php');
    } catch (error) {
      // Ignore errors
    }
  });

  test.describe('Database Integration', () => {

    test('should have database initialized when accessing web interface', async ({ page }) => {
      await page.goto('/');

      // Should show login form (means database is working)
      await expect(page.locator('h1')).toContainText('Uptime Monitor');
      await expect(page.locator('input[name="username"]')).toBeVisible();
      await expect(page.locator('input[name="password"]')).toBeVisible();
    });

    test('should be able to login with default admin user', async ({ page }) => {
      await page.goto('/');

      // Try to login with default password (fallback to old system)
      await page.fill('input[name="username"]', 'admin');
      await page.fill('input[name="password"]', 'admin');
      await page.click('input[type="submit"]');

      // Should redirect to dashboard
      await expect(page).toHaveURL(/dashboard\.php/);
      await expect(page.locator('h1').first()).toContainText('Dashboard');
    });

  });

  test.describe('User Registration via Web Interface', () => {

    test('should access registration page', async ({ page }) => {
      await page.goto('/register.php');

      await expect(page.locator('h1')).toContainText('Register');
      await expect(page.locator('input[name="username"]')).toBeVisible();
      await expect(page.locator('input[name="email"]')).toBeVisible();
      await expect(page.locator('input[name="password"]')).toBeVisible();
      await expect(page.locator('input[name="confirm_password"]')).toBeVisible();
    });

    test('should register new user successfully via web interface', async ({ page }) => {
      await page.goto('/register.php');

      const timestamp = Date.now();
      await page.fill('input[name="username"]', `testuser${timestamp}`);
      await page.fill('input[name="email"]', `test${timestamp}@example.com`);
      await page.fill('input[name="password"]', 'password123');
      await page.fill('input[name="confirm_password"]', 'password123');

      await page.click('input[type="submit"]');

      // Should show success message
      await expect(page.locator('.success')).toContainText('registered successfully');
    });

    test('should be able to login with registered user', async ({ page }) => {
      // First register a user
      await page.goto('/register.php');

      const timestamp = Date.now();
      const username = `testuser${timestamp}`;
      const password = 'password123';

      await page.fill('input[name="username"]', username);
      await page.fill('input[name="email"]', `test${timestamp}@example.com`);
      await page.fill('input[name="password"]', password);
      await page.fill('input[name="confirm_password"]', password);
      await page.click('input[type="submit"]');

      // Wait for success message
      await expect(page.locator('.success')).toBeVisible();

      // Now try to login
      await page.goto('/');
      await page.fill('input[name="username"]', username);
      await page.fill('input[name="password"]', password);
      await page.click('input[type="submit"]');

      // Should redirect to dashboard
      await expect(page).toHaveURL(/dashboard\.php/);
      await expect(page.locator('h1').first()).toContainText('Dashboard');
    });

  });

});

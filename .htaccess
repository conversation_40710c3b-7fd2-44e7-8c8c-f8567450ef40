# Uptime Monitor - Apache Configuration
# This file provides security and URL rewriting for Apache servers

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Prevent access to sensitive files
<Files "*.json">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files "config.php">
    Require all denied
</Files>

<Files "check-monitors.php">
    # Allow execution from command line but not web access
    <RequireAll>
        Require all denied
    </RequireAll>
</Files>

# Prevent access to test files in production
<FilesMatch "\.(spec|test)\.js$">
    Require all denied
</FilesMatch>

<Files "package.json">
    Require all denied
</Files>

<Files "package-lock.json">
    Require all denied
</Files>

<Files "playwright.config.js">
    Require all denied
</Files>

# Prevent directory browsing
Options -Indexes

# Optional: Clean URLs (remove .php extension)
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Redirect to HTTPS (uncomment for production)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Remove .php extension from URLs
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^([^.]+)$ $1.php [NC,L]
    
    # Redirect .php URLs to clean URLs
    RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
    RewriteRule ^ /%1 [NC,L,R=301]
</IfModule>

# PHP Configuration (if allowed)
<IfModule mod_php7.c>
    # Hide PHP version
    php_flag expose_php off
    
    # Security settings
    php_flag display_errors off
    php_flag log_errors on
    
    # Session security
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
</IfModule>

# Compression (optional)
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control for static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-icon "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 month"
</IfModule>

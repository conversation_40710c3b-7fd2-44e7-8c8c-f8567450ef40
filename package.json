{"name": "uptime-monitor1", "version": "1.0.0", "description": "> Purpose: Create a minimal web application to monitor website availability, notify via email if down, and support user login. It must run on a server and execute checks automatically via cron. UI should be kept minimal. No extra features beyond requirements.", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui"}, "repository": {"type": "git", "url": "git+https://github.com/Andre05-vikk/uptime-monitor1.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/Andre05-vikk/uptime-monitor1/issues"}, "homepage": "https://github.com/Andre05-vikk/uptime-monitor1#readme", "devDependencies": {"@playwright/test": "^1.52.0"}}
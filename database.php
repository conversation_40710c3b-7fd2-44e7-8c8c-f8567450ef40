<?php
/**
 * SQLite Database Management for Uptime Monitor
 * 
 * This file handles all database operations including:
 * - Database initialization
 * - User management (registration, authentication)
 * - Database migrations
 */

// Database configuration
$db_file = getenv('DATABASE_FILE') ?: __DIR__ . '/uptime_monitor.db';
define('DATABASE_FILE', $db_file);

/**
 * Get database connection
 */
function get_database_connection() {
    try {
        $pdo = new PDO('sqlite:' . DATABASE_FILE);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        throw new Exception("Database connection failed");
    }
}

/**
 * Initialize database with required tables
 */
function initialize_database() {
    $pdo = get_database_connection();
    
    // Create users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            last_login DATETIME NULL
        )
    ");
    
    // Create user_sessions table (for future use)
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            session_token VARCHAR(255) UNIQUE NOT NULL,
            expires_at DATETIME NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            ip_address VARCHAR(45),
            user_agent TEXT,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");
    
    // Create monitors table (future migration from JSON)
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS monitors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            url VARCHAR(500) NOT NULL,
            email VARCHAR(100) NOT NULL,
            status VARCHAR(20) DEFAULT 'active',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_check DATETIME NULL,
            last_status VARCHAR(10) NULL,
            last_response_time INTEGER NULL,
            last_http_code INTEGER NULL,
            last_error TEXT NULL,
            incident_notified BOOLEAN DEFAULT 0,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");
    
    // Create indexes for better performance
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(session_token)");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_sessions_expires ON user_sessions(expires_at)");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_monitors_user ON monitors(user_id)");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_monitors_status ON monitors(status)");
    
    // Create default admin user if no users exist
    create_default_admin_user();
    
    return true;
}

/**
 * Create default admin user
 */
function create_default_admin_user() {
    $pdo = get_database_connection();
    
    // Check if any users exist
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        // Create default admin user
        $username = 'admin';
        $email = '<EMAIL>';
        $password = 'admin123'; // Changed to meet minimum length requirement
        
        $result = register_user($username, $email, $password);
        
        if ($result['success']) {
            error_log("Default admin user created: username=admin, password=admin");
        } else {
            error_log("Failed to create default admin user: " . $result['message']);
        }
    }
}

/**
 * Register a new user
 */
function register_user($username, $email, $password) {
    // Validate input
    if (empty($username) || empty($email) || empty($password)) {
        return ['success' => false, 'message' => 'All fields are required'];
    }
    
    if (strlen($username) < 3 || strlen($username) > 50) {
        return ['success' => false, 'message' => 'Username must be between 3 and 50 characters'];
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return ['success' => false, 'message' => 'Invalid email format'];
    }
    
    if (strlen($password) < 6) {
        return ['success' => false, 'message' => 'Password must be at least 6 characters long'];
    }
    
    try {
        $pdo = get_database_connection();
        
        // Check if username or email already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $email]);
        
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Username or email already exists'];
        }
        
        // Hash password
        $password_hash = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert new user
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password_hash) 
            VALUES (?, ?, ?)
        ");
        
        $stmt->execute([$username, $email, $password_hash]);
        
        return [
            'success' => true, 
            'message' => 'User registered successfully',
            'user_id' => $pdo->lastInsertId()
        ];
        
    } catch (PDOException $e) {
        error_log("User registration failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Registration failed. Please try again.'];
    }
}

/**
 * Authenticate user with username/email and password
 */
function authenticate_user_db($login, $password) {
    try {
        $pdo = get_database_connection();
        
        // Find user by username or email
        $stmt = $pdo->prepare("
            SELECT id, username, email, password_hash, is_active 
            FROM users 
            WHERE (username = ? OR email = ?) AND is_active = 1
        ");
        $stmt->execute([$login, $login]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return false;
        }
        
        // Verify password
        if (!password_verify($password, $user['password_hash'])) {
            return false;
        }
        
        // Update last login
        $stmt = $pdo->prepare("UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->execute([$user['id']]);
        
        // Set session variables
        $_SESSION['logged_in'] = true;
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        
        return true;
        
    } catch (PDOException $e) {
        error_log("Authentication failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user information by ID
 */
function get_user_by_id($user_id) {
    try {
        $pdo = get_database_connection();
        
        $stmt = $pdo->prepare("
            SELECT id, username, email, created_at, last_login, is_active 
            FROM users 
            WHERE id = ? AND is_active = 1
        ");
        $stmt->execute([$user_id]);
        
        return $stmt->fetch();
        
    } catch (PDOException $e) {
        error_log("Get user failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user information by username
 */
function get_user_by_username($username) {
    try {
        $pdo = get_database_connection();
        
        $stmt = $pdo->prepare("
            SELECT id, username, email, created_at, last_login, is_active 
            FROM users 
            WHERE username = ? AND is_active = 1
        ");
        $stmt->execute([$username]);
        
        return $stmt->fetch();
        
    } catch (PDOException $e) {
        error_log("Get user failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Update user password
 */
function update_user_password($user_id, $new_password) {
    if (strlen($new_password) < 6) {
        return ['success' => false, 'message' => 'Password must be at least 6 characters long'];
    }
    
    try {
        $pdo = get_database_connection();
        
        $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("
            UPDATE users 
            SET password_hash = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ");
        
        $stmt->execute([$password_hash, $user_id]);
        
        return ['success' => true, 'message' => 'Password updated successfully'];
        
    } catch (PDOException $e) {
        error_log("Password update failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Password update failed'];
    }
}

/**
 * Get all users (admin function)
 */
function get_all_users() {
    try {
        $pdo = get_database_connection();
        
        $stmt = $pdo->query("
            SELECT id, username, email, created_at, last_login, is_active 
            FROM users 
            ORDER BY created_at DESC
        ");
        
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("Get all users failed: " . $e->getMessage());
        return [];
    }
}

/**
 * Check if database exists and is properly initialized
 */
function is_database_initialized() {
    try {
        $pdo = get_database_connection();
        
        // Check if users table exists
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='users'");
        $result = $stmt->fetch();
        
        return $result !== false;
        
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Migrate from JSON users file to SQLite database
 */
function migrate_users_from_json() {
    $users_file = __DIR__ . '/users.json';
    
    if (!file_exists($users_file)) {
        return ['success' => true, 'message' => 'No JSON users file to migrate'];
    }
    
    try {
        $json_users = json_decode(file_get_contents($users_file), true);
        
        if (!$json_users) {
            return ['success' => true, 'message' => 'No users in JSON file'];
        }
        
        $migrated = 0;
        $errors = [];
        
        foreach ($json_users as $username => $password_hash) {
            // Try to register user (password hash is already hashed)
            $pdo = get_database_connection();
            
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password_hash) 
                    VALUES (?, ?, ?)
                ");
                
                $email = $username . '@migrated.local';
                $stmt->execute([$username, $email, $password_hash]);
                $migrated++;
                
            } catch (PDOException $e) {
                $errors[] = "Failed to migrate user '$username': " . $e->getMessage();
            }
        }
        
        // Backup and remove JSON file
        if ($migrated > 0) {
            rename($users_file, $users_file . '.backup.' . date('Y-m-d-H-i-s'));
        }
        
        return [
            'success' => true,
            'message' => "Migrated $migrated users from JSON file",
            'errors' => $errors
        ];
        
    } catch (Exception $e) {
        error_log("JSON migration failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Migration failed: ' . $e->getMessage()];
    }
}

// Initialize database when this file is included
if (!is_database_initialized()) {
    initialize_database();
    
    // Try to migrate existing users
    $migration_result = migrate_users_from_json();
    if (!empty($migration_result['errors'])) {
        error_log("Migration warnings: " . implode(', ', $migration_result['errors']));
    }
}

?>

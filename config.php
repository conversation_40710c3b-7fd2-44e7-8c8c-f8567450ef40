<?php
// Basic configuration for uptime monitor application

// Start session for authentication
session_start();

// Simple file-based user storage (as per requirements)
define('USERS_FILE', __DIR__ . '/users.json');

// Monitor data storage
define('MONITORS_FILE', __DIR__ . '/monitors.json');

// Default admin user (you can add more users to users.json)
$default_users = [
    'admin' => password_hash('admin', PASSWORD_DEFAULT)
];

// Create users file if it doesn't exist
if (!file_exists(USERS_FILE)) {
    file_put_contents(USERS_FILE, json_encode($default_users, JSON_PRETTY_PRINT));
}

// Function to authenticate user
function authenticate_user($username, $password) {
    if (!file_exists(USERS_FILE)) {
        return false;
    }
    
    $users = json_decode(file_get_contents(USERS_FILE), true);
    
    if (isset($users[$username]) && password_verify($password, $users[$username])) {
        $_SESSION['logged_in'] = true;
        $_SESSION['username'] = $username;
        return true;
    }
    
    return false;
}

// Function to check if user is logged in
function is_logged_in() {
    return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
}

// Function to logout user
function logout_user() {
    session_unset();
    session_destroy();
}

// Function to require login (redirect if not logged in)
function require_login() {
    if (!is_logged_in()) {
        header('Location: index.php');
        exit();
    }
}

// Function to get all monitors
function get_monitors() {
    if (!file_exists(MONITORS_FILE)) {
        return [];
    }
    
    $content = file_get_contents(MONITORS_FILE);
    return json_decode($content, true) ?: [];
}

// Function to save monitors
function save_monitors($monitors) {
    return file_put_contents(MONITORS_FILE, json_encode($monitors, JSON_PRETTY_PRINT));
}

// Function to validate URL format
function validate_url($url) {
    // Check if URL is valid and has http/https protocol
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return false;
    }
    
    $parsed = parse_url($url);
    return isset($parsed['scheme']) && in_array($parsed['scheme'], ['http', 'https']);
}

// Function to validate email format
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Function to add new monitor
function add_monitor($url, $email) {
    // Validate input
    if (!validate_url($url)) {
        return ['success' => false, 'message' => 'Invalid URL format. Please use http:// or https://'];
    }

    if (!validate_email($email)) {
        return ['success' => false, 'message' => 'Invalid email format.'];
    }

    // Get existing monitors
    $monitors = get_monitors();

    // Check for duplicates
    foreach ($monitors as $monitor) {
        if ($monitor['url'] === $url && $monitor['email'] === $email) {
            return ['success' => false, 'message' => 'This URL and email combination already exists.'];
        }
    }

    // Add new monitor
    $new_monitor = [
        'url' => $url,
        'email' => $email,
        'added_at' => date('Y-m-d H:i:s'),
        'status' => 'active',
        'last_check' => null,
        'last_status' => null,
        'last_response_time' => null
    ];

    $monitors[] = $new_monitor;

    // Save to file
    if (save_monitors($monitors)) {
        return ['success' => true, 'message' => 'Monitor added successfully!'];
    } else {
        return ['success' => false, 'message' => 'Failed to save monitor. Please check file permissions.'];
    }
}

// Logging configuration
define('LOGS_DIR', __DIR__ . '/logs');

// Function to ensure logs directory exists
function ensure_logs_directory() {
    if (!is_dir(LOGS_DIR)) {
        mkdir(LOGS_DIR, 0755, true);
    }
}

// Function to log monitoring events
function log_monitor_event($message, $level = 'INFO') {
    ensure_logs_directory();

    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;

    $log_file = LOGS_DIR . '/monitor-' . date('Y-m-d') . '.log';
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// Function to check URL status
function check_url_status($url, $timeout = 10) {
    $start_time = microtime(true);

    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Uptime Monitor Bot/1.0');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For testing purposes
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $response_time = round((microtime(true) - $start_time) * 1000, 2); // in milliseconds

    curl_close($ch);

    return [
        'success' => $response !== false && $http_code >= 200 && $http_code < 400,
        'http_code' => $http_code,
        'response_time' => $response_time,
        'error' => $error,
        'checked_at' => date('Y-m-d H:i:s')
    ];
}

// Function to update monitor status
function update_monitor_status($monitor_index, $check_result) {
    $monitors = get_monitors();

    if (isset($monitors[$monitor_index])) {
        $monitors[$monitor_index]['last_check'] = $check_result['checked_at'];
        $monitors[$monitor_index]['last_status'] = $check_result['success'] ? 'up' : 'down';
        $monitors[$monitor_index]['last_response_time'] = $check_result['response_time'];
        $monitors[$monitor_index]['last_http_code'] = $check_result['http_code'];
        $monitors[$monitor_index]['last_error'] = $check_result['error'];

        save_monitors($monitors);
        return true;
    }

    return false;
}
?>

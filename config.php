<?php
// Basic configuration for uptime monitor application

// Start session for authentication (only if not running from command line)
if (php_sapi_name() !== 'cli') {
    session_start();
}

// Simple file-based user storage (as per requirements)
define('USERS_FILE', __DIR__ . '/users.json');

// Monitor data storage
$monitors_file = getenv('MONITORS_FILE') ?: __DIR__ . '/monitors.json';
define('MONITORS_FILE', $monitors_file);

// Default admin user (you can add more users to users.json)
$default_users = [
    'admin' => password_hash('admin', PASSWORD_DEFAULT)
];

// Create users file if it doesn't exist
if (!file_exists(USERS_FILE)) {
    file_put_contents(USERS_FILE, json_encode($default_users, JSON_PRETTY_PRINT));
}

// Function to authenticate user
function authenticate_user($username, $password) {
    if (!file_exists(USERS_FILE)) {
        return false;
    }
    
    $users = json_decode(file_get_contents(USERS_FILE), true);
    
    if (isset($users[$username]) && password_verify($password, $users[$username])) {
        $_SESSION['logged_in'] = true;
        $_SESSION['username'] = $username;
        return true;
    }
    
    return false;
}

// Function to check if user is logged in
function is_logged_in() {
    return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
}

// Function to logout user
function logout_user() {
    session_unset();
    session_destroy();
}

// Function to require login (redirect if not logged in)
function require_login() {
    if (!is_logged_in()) {
        header('Location: index.php');
        exit();
    }
}

// Function to get all monitors
function get_monitors() {
    if (!file_exists(MONITORS_FILE)) {
        return [];
    }
    
    $content = file_get_contents(MONITORS_FILE);
    return json_decode($content, true) ?: [];
}

// Function to save monitors
function save_monitors($monitors) {
    return file_put_contents(MONITORS_FILE, json_encode($monitors, JSON_PRETTY_PRINT));
}

// Function to validate URL format
function validate_url($url) {
    // Check if URL is valid and has http/https protocol
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return false;
    }
    
    $parsed = parse_url($url);
    return isset($parsed['scheme']) && in_array($parsed['scheme'], ['http', 'https']);
}

// Function to validate email format
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Function to add new monitor
function add_monitor($url, $email) {
    // Validate input
    if (!validate_url($url)) {
        return ['success' => false, 'message' => 'Invalid URL format. Please use http:// or https://'];
    }

    if (!validate_email($email)) {
        return ['success' => false, 'message' => 'Invalid email format.'];
    }

    // Get existing monitors
    $monitors = get_monitors();

    // Check for duplicates
    foreach ($monitors as $monitor) {
        if ($monitor['url'] === $url && $monitor['email'] === $email) {
            return ['success' => false, 'message' => 'This URL and email combination already exists.'];
        }
    }

    // Add new monitor
    $new_monitor = [
        'url' => $url,
        'email' => $email,
        'added_at' => date('Y-m-d H:i:s'),
        'status' => 'active',
        'last_check' => null,
        'last_status' => null,
        'last_response_time' => null
    ];

    $monitors[] = $new_monitor;

    // Save to file
    if (save_monitors($monitors)) {
        return ['success' => true, 'message' => 'Monitor added successfully!'];
    } else {
        return ['success' => false, 'message' => 'Failed to save monitor. Please check file permissions.'];
    }
}

// Logging configuration
$logs_dir = getenv('LOGS_DIR') ?: __DIR__ . '/logs';
define('LOGS_DIR', $logs_dir);

// Function to ensure logs directory exists
function ensure_logs_directory() {
    if (!is_dir(LOGS_DIR)) {
        mkdir(LOGS_DIR, 0755, true);
    }
}

// Function to log monitoring events
function log_monitor_event($message, $level = 'INFO') {
    ensure_logs_directory();

    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;

    $log_file = LOGS_DIR . '/monitor-' . date('Y-m-d') . '.log';
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// Function to check URL status
function check_url_status($url, $timeout = 10) {
    $start_time = microtime(true);

    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Uptime Monitor Bot/1.0');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For testing purposes
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $response_time = round((microtime(true) - $start_time) * 1000, 2); // in milliseconds

    curl_close($ch);

    return [
        'success' => $response !== false && $http_code >= 200 && $http_code < 400,
        'http_code' => $http_code,
        'response_time' => $response_time,
        'error' => $error,
        'checked_at' => date('Y-m-d H:i:s')
    ];
}

// Function to update monitor status
function update_monitor_status($monitor_index, $check_result) {
    $monitors = get_monitors();

    if (isset($monitors[$monitor_index])) {
        $previous_status = $monitors[$monitor_index]['last_status'] ?? null;
        $current_status = $check_result['success'] ? 'up' : 'down';

        $monitors[$monitor_index]['last_check'] = $check_result['checked_at'];
        $monitors[$monitor_index]['last_status'] = $current_status;
        $monitors[$monitor_index]['last_response_time'] = $check_result['response_time'];
        $monitors[$monitor_index]['last_http_code'] = $check_result['http_code'];
        $monitors[$monitor_index]['last_error'] = $check_result['error'];

        // Track incident state for email notifications
        if ($current_status === 'down' && $previous_status !== 'down') {
            // New incident - mark for email notification
            $monitors[$monitor_index]['incident_notified'] = false;
            $monitors[$monitor_index]['incident_started'] = $check_result['checked_at'];
        } elseif ($current_status === 'up' && $previous_status === 'down') {
            // Incident resolved - reset notification flag
            $monitors[$monitor_index]['incident_notified'] = false;
            $monitors[$monitor_index]['incident_resolved'] = $check_result['checked_at'];
        }

        $result = save_monitors($monitors);
        if ($result === false) {
            error_log("Failed to save monitors to file");
        }
        return $result !== false;
    }

    return false;
}

// Email configuration
define('EMAIL_FROM', '<EMAIL>');
define('EMAIL_FROM_NAME', 'Uptime Monitor');

// Enable test mode for emails when running tests
if (getenv('EMAIL_TEST_MODE') === 'true') {
    define('EMAIL_TEST_MODE', true);
}

// Function to send email notification
function send_email($to, $subject, $message, $headers = []) {
    // Default headers
    $default_headers = [
        'From' => EMAIL_FROM_NAME . ' <' . EMAIL_FROM . '>',
        'Reply-To' => EMAIL_FROM,
        'X-Mailer' => 'PHP/' . phpversion(),
        'Content-Type' => 'text/html; charset=UTF-8'
    ];

    $all_headers = array_merge($default_headers, $headers);
    $header_string = '';
    foreach ($all_headers as $key => $value) {
        $header_string .= $key . ': ' . $value . "\r\n";
    }

    // For testing purposes, we can use a mock or file-based email system
    if (defined('EMAIL_TEST_MODE') && EMAIL_TEST_MODE) {
        return send_test_email($to, $subject, $message, $header_string);
    }

    // Use PHP mail() function
    return mail($to, $subject, $message, $header_string);
}

// Function for test email (saves to file instead of sending)
function send_test_email($to, $subject, $message, $headers) {
    ensure_logs_directory();

    $email_log = LOGS_DIR . '/emails-' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');

    $email_content = "[$timestamp] EMAIL SENT\n";
    $email_content .= "To: $to\n";
    $email_content .= "Subject: $subject\n";
    $email_content .= "Headers: " . str_replace("\r\n", " | ", $headers) . "\n";
    $email_content .= "Message: $message\n";
    $email_content .= "---\n\n";

    return file_put_contents($email_log, $email_content, FILE_APPEND | LOCK_EX) !== false;
}

// Function to compose failure notification email
function compose_failure_email($monitor, $check_result) {
    $url = htmlspecialchars($monitor['url']);
    $timestamp = $check_result['checked_at'];
    $error_msg = $check_result['error'] ?: "HTTP " . $check_result['http_code'];
    $response_time = $check_result['response_time'];

    $subject = "🚨 Website Down Alert: " . $monitor['url'];

    $message = "
    <html>
    <head>
        <title>Website Down Alert</title>
    </head>
    <body style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
        <div style='background-color: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px; margin-bottom: 20px;'>
            <h2 style='margin: 0 0 10px 0;'>🚨 Website Down Alert</h2>
            <p style='margin: 0; font-size: 16px;'>Your monitored website is currently unavailable.</p>
        </div>

        <div style='background-color: #f8f9fa; padding: 20px; border-radius: 5px;'>
            <h3 style='margin-top: 0;'>Incident Details:</h3>
            <table style='width: 100%; border-collapse: collapse;'>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Website URL:</td>
                    <td style='padding: 8px 0;'><a href='{$url}'>{$url}</a></td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Detected at:</td>
                    <td style='padding: 8px 0;'>{$timestamp}</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Error:</td>
                    <td style='padding: 8px 0; color: #dc3545;'>{$error_msg}</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Response Time:</td>
                    <td style='padding: 8px 0;'>{$response_time}ms</td>
                </tr>
            </table>
        </div>

        <div style='margin-top: 20px; padding: 15px; background-color: #e7f3ff; border-radius: 5px;'>
            <p style='margin: 0; font-size: 14px; color: #0c5460;'>
                <strong>What happens next?</strong><br>
                We will continue monitoring your website and notify you when it's back online.
                This alert will not be sent again for this incident.
            </p>
        </div>

        <div style='margin-top: 20px; text-align: center; font-size: 12px; color: #6c757d;'>
            <p>This is an automated message from Uptime Monitor.<br>
            You are receiving this because you requested monitoring for this website.</p>
        </div>
    </body>
    </html>";

    return ['subject' => $subject, 'message' => $message];
}

// Function to send failure notification
function send_failure_notification($monitor, $check_result) {
    // Check if we should send notification (only once per incident)
    if (isset($monitor['incident_notified']) && $monitor['incident_notified']) {
        log_monitor_event("Skipping email notification - already sent for this incident: {$monitor['url']}", 'INFO');
        return false;
    }

    $email_data = compose_failure_email($monitor, $check_result);

    $result = send_email(
        $monitor['email'],
        $email_data['subject'],
        $email_data['message']
    );

    if ($result) {
        log_monitor_event("Email notification sent to {$monitor['email']} for {$monitor['url']}", 'INFO');
        return true;
    } else {
        log_monitor_event("Failed to send email notification to {$monitor['email']} for {$monitor['url']}", 'ERROR');
        return false;
    }
}

// Function to mark incident as notified
function mark_incident_notified($monitor_index) {
    $monitors = get_monitors();

    if (isset($monitors[$monitor_index])) {
        $monitors[$monitor_index]['incident_notified'] = true;
        return save_monitors($monitors);
    }

    return false;
}
?>

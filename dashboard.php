<?php
require_once 'config.php';

// Require user to be logged in
require_login();

// Handle form submission
$message = '';
$message_type = '';

if ($_POST && isset($_POST['url'], $_POST['email'])) {
    $url = trim($_POST['url']);
    $email = trim($_POST['email']);
    
    $result = add_monitor($url, $email);
    $message = $result['message'];
    $message_type = $result['success'] ? 'success' : 'error';
}

// Get current username and monitors
$username = $_SESSION['username'] ?? '';
$monitors = get_monitors();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Uptime Monitor - Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f9f9f9;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border: 1px solid #ccc;
        }
        h1, h2 {
            color: #333;
        }
        .header {
            border-bottom: 1px solid #ccc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .user-info {
            text-align: right;
            margin-bottom: 10px;
        }
        .logout-link {
            color: #cc0000;
            text-decoration: none;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="url"], input[type="email"] {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            box-sizing: border-box;
        }
        input[type="submit"] {
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            border: none;
            cursor: pointer;
        }
        .success {
            background-color: #e6ffe6;
            color: #006600;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #006600;
        }
        .error {
            background-color: #ffe6e6;
            color: #cc0000;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #cc0000;
        }
        .monitor-list {
            margin-top: 30px;
        }
        .monitor-item {
            padding: 10px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
            background-color: #f5f5f5;
        }
        .monitor-url {
            font-weight: bold;
            color: #0066cc;
        }
        .monitor-email {
            color: #666;
            font-size: 14px;
        }
        .monitor-date {
            color: #999;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="user-info">
                Welcome, <strong><?php echo htmlspecialchars($username); ?></strong> |
                <a href="logout.php" class="logout-link">Logout</a>
            </div>
            <h1>Monitor Dashboard</h1>
        </div>

        <h2>Add Website Monitor</h2>

        <?php if ($message): ?>
            <div class="<?php echo $message_type; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <label for="url">Website URL to monitor:</label>
            <input type="url" id="url" name="url" placeholder="https://example.com" required>

            <label for="email">Notification email:</label>
            <input type="email" id="email" name="email" placeholder="<EMAIL>" required>

            <input type="submit" value="Add Monitor">
        </form>

        <?php if (!empty($monitors)): ?>
        <div class="monitor-list">
            <h2>Current Monitors</h2>
            <?php foreach ($monitors as $monitor): ?>
                <div class="monitor-item">
                    <div class="monitor-url"><?php echo htmlspecialchars($monitor['url']); ?></div>
                    <div class="monitor-email">Email: <?php echo htmlspecialchars($monitor['email']); ?></div>
                    <div class="monitor-date">Added: <?php echo htmlspecialchars($monitor['added_at']); ?></div>
                    <?php if (isset($monitor['last_status'])): ?>
                        <div class="monitor-status">
                            Status: <?php echo $monitor['last_status'] === 'up' ? '✓ Online' : '✗ Offline'; ?>
                            <?php if (isset($monitor['last_check'])): ?>
                                (Last checked: <?php echo htmlspecialchars($monitor['last_check']); ?>)
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>

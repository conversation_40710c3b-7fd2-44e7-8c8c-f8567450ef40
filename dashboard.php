<?php
require_once 'config.php';

// Require user to be logged in
require_login();

// Handle form submission
$message = '';
$message_type = '';

if ($_POST && isset($_POST['url'], $_POST['email'])) {
    $url = trim($_POST['url']);
    $email = trim($_POST['email']);
    
    $result = add_monitor($url, $email);
    $message = $result['message'];
    $message_type = $result['success'] ? 'success' : 'error';
}

// Get current username and monitors
$username = $_SESSION['username'] ?? '';
$monitors = get_monitors();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Uptime Monitor - Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f9f9f9;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border: 1px solid #ccc;
        }
        h1, h2 {
            color: #333;
        }
        .header {
            border-bottom: 1px solid #ccc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .user-info {
            text-align: right;
            margin-bottom: 10px;
        }
        .logout-link {
            color: #cc0000;
            text-decoration: none;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="url"], input[type="email"] {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            box-sizing: border-box;
        }
        input[type="submit"] {
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            border: none;
            cursor: pointer;
        }
        .success {
            background-color: #e6ffe6;
            color: #006600;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #006600;
        }
        .error {
            background-color: #ffe6e6;
            color: #cc0000;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #cc0000;
        }
        .monitor-list {
            margin-top: 30px;
        }
        .monitor-item {
            padding: 15px;
            border: 1px solid #ddd;
            margin-bottom: 15px;
            background-color: #f5f5f5;
            border-radius: 5px;
            position: relative;
        }
        .monitor-url {
            font-weight: bold;
            color: #0066cc;
        }
        .monitor-email {
            color: #666;
            font-size: 14px;
        }
        .monitor-date {
            color: #999;
            font-size: 12px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        .status-online {
            background-color: #28a745;
            box-shadow: 0 0 5px rgba(40, 167, 69, 0.5);
        }
        .status-offline {
            background-color: #dc3545;
            box-shadow: 0 0 5px rgba(220, 53, 69, 0.5);
        }
        .status-checking {
            background-color: #ffc107;
            box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .monitor-status {
            display: flex;
            align-items: center;
            margin: 8px 0;
            font-weight: bold;
        }
        .status-online-text { color: #28a745; }
        .status-offline-text { color: #dc3545; }
        .status-checking-text { color: #ffc107; }
        .response-time {
            font-size: 12px;
            color: #666;
            margin-left: 10px;
            font-weight: normal;
        }
        .last-check {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }
        .refresh-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }
        .refresh-btn:hover {
            background: #138496;
        }
        .auto-refresh {
            margin-bottom: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 5px;
            text-align: center;
        }
        .refresh-controls {
            margin-bottom: 20px;
        }
        .refresh-all-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .refresh-all-btn:hover {
            background: #0056b3;
        }
    </style>
    <script>
        let autoRefreshInterval;
        let isAutoRefreshEnabled = false;

        function checkMonitorStatus(url, monitorElement) {
            const statusElement = monitorElement.querySelector('.monitor-status');
            const indicator = statusElement.querySelector('.status-indicator');
            const statusText = statusElement.querySelector('.status-text');
            const responseTime = statusElement.querySelector('.response-time');
            const lastCheck = monitorElement.querySelector('.last-check');

            // Set checking status
            indicator.className = 'status-indicator status-checking';
            statusText.textContent = 'Checking...';
            statusText.className = 'status-text status-checking-text';
            responseTime.textContent = '';

            const startTime = Date.now();

            // Use a simple fetch to check if URL is accessible
            fetch('api/check-status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url: url })
            })
            .then(response => response.json())
            .then(data => {
                const endTime = Date.now();
                const responseTimeMs = endTime - startTime;

                if (data.status === 'online') {
                    indicator.className = 'status-indicator status-online';
                    statusText.textContent = 'Online';
                    statusText.className = 'status-text status-online-text';
                    responseTime.textContent = `(${responseTimeMs}ms)`;
                } else {
                    indicator.className = 'status-indicator status-offline';
                    statusText.textContent = 'Offline';
                    statusText.className = 'status-text status-offline-text';
                    responseTime.textContent = `(${data.error || 'No response'})`;
                }

                lastCheck.textContent = `Last checked: ${new Date().toLocaleString()}`;
            })
            .catch(error => {
                const endTime = Date.now();
                const responseTimeMs = endTime - startTime;

                indicator.className = 'status-indicator status-offline';
                statusText.textContent = 'Offline';
                statusText.className = 'status-text status-offline-text';
                responseTime.textContent = `(${responseTimeMs}ms - Error)`;
                lastCheck.textContent = `Last checked: ${new Date().toLocaleString()}`;
            });
        }

        function refreshAllMonitors() {
            const monitors = document.querySelectorAll('.monitor-item');
            monitors.forEach(monitor => {
                const url = monitor.querySelector('.monitor-url').textContent;
                checkMonitorStatus(url, monitor);
            });
        }

        function toggleAutoRefresh() {
            const button = document.getElementById('auto-refresh-btn');
            const status = document.getElementById('auto-refresh-status');

            if (isAutoRefreshEnabled) {
                clearInterval(autoRefreshInterval);
                isAutoRefreshEnabled = false;
                button.textContent = 'Enable Auto Refresh';
                button.style.backgroundColor = '#28a745';
                status.textContent = 'Auto refresh is OFF';
            } else {
                autoRefreshInterval = setInterval(refreshAllMonitors, 30000); // 30 seconds
                isAutoRefreshEnabled = true;
                button.textContent = 'Disable Auto Refresh';
                button.style.backgroundColor = '#dc3545';
                status.textContent = 'Auto refresh is ON (every 30 seconds)';
                refreshAllMonitors(); // Initial check
            }
        }

        // Auto-check all monitors when page loads
        document.addEventListener('DOMContentLoaded', function() {
            refreshAllMonitors();
        });
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="user-info">
                Welcome, <strong><?php echo htmlspecialchars($username); ?></strong> |
                <a href="logout.php" class="logout-link">Logout</a>
            </div>
            <h1>Monitor Dashboard</h1>
        </div>

        <h2>Add Website Monitor</h2>

        <?php if ($message): ?>
            <div class="<?php echo $message_type; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <label for="url">Website URL to monitor:</label>
            <input type="url" id="url" name="url" placeholder="https://example.com" required>

            <label for="email">Notification email:</label>
            <input type="email" id="email" name="email" placeholder="<EMAIL>" required>

            <input type="submit" value="Add Monitor">
        </form>

        <?php if (!empty($monitors)): ?>
        <div class="monitor-list">
            <h2>Current Monitors</h2>

            <div class="refresh-controls">
                <button onclick="refreshAllMonitors()" class="refresh-all-btn">Refresh All</button>
                <button onclick="toggleAutoRefresh()" id="auto-refresh-btn" class="refresh-all-btn" style="background-color: #28a745;">Enable Auto Refresh</button>
            </div>

            <div class="auto-refresh">
                <span id="auto-refresh-status">Auto refresh is OFF</span>
            </div>

            <?php foreach ($monitors as $monitor): ?>
                <div class="monitor-item">
                    <div class="monitor-url"><?php echo htmlspecialchars($monitor['url']); ?></div>
                    <div class="monitor-email">Email: <?php echo htmlspecialchars($monitor['email']); ?></div>
                    <div class="monitor-date">Added: <?php echo htmlspecialchars($monitor['added_at']); ?></div>

                    <div class="monitor-status">
                        <span class="status-indicator status-checking"></span>
                        <span class="status-text status-checking-text">Loading...</span>
                        <span class="response-time"></span>
                        <button onclick="checkMonitorStatus('<?php echo htmlspecialchars($monitor['url']); ?>', this.parentElement.parentElement)" class="refresh-btn">Check Now</button>
                    </div>

                    <div class="last-check">Last checked: Never</div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>

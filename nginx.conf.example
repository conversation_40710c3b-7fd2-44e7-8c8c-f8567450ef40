# Uptime Monitor - Nginx Configuration Example
# Copy this configuration to your Nginx sites-available directory
# and adjust paths and domain names as needed

server {
    listen 80;
    listen [::]:80;
    server_name your-domain.com www.your-domain.com;
    
    # Redirect HTTP to HTTPS (uncomment for production)
    # return 301 https://$server_name$request_uri;
    
    # Document root
    root /var/www/html/uptime-monitor;
    index index.php index.html;
    
    # Security headers
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Hide Nginx version
    server_tokens off;
    
    # Main location block
    location / {
        try_files $uri $uri/ $uri.php?$query_string;
    }
    
    # PHP processing
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;  # Adjust PHP version as needed
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Security
        fastcgi_hide_header X-Powered-By;
    }
    
    # Prevent access to sensitive files
    location ~ \.(json|log)$ {
        deny all;
        return 404;
    }
    
    location = /config.php {
        deny all;
        return 404;
    }
    
    location = /check-monitors.php {
        # Prevent web access to cron script
        deny all;
        return 404;
    }
    
    # Prevent access to test and development files
    location ~ \.(spec|test)\.js$ {
        deny all;
        return 404;
    }
    
    location ~ ^/(package\.json|package-lock\.json|playwright\.config\.js|\.git|tests/) {
        deny all;
        return 404;
    }
    
    # Prevent access to hidden files
    location ~ /\. {
        deny all;
        return 404;
    }
    
    # Static file caching
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # Rate limiting (optional)
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    
    location = /index.php {
        limit_req zone=login burst=3 nodelay;
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # Logging
    access_log /var/log/nginx/uptime-monitor.access.log;
    error_log /var/log/nginx/uptime-monitor.error.log;
}

# HTTPS configuration (uncomment and configure for production)
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     server_name your-domain.com www.your-domain.com;
#     
#     # SSL configuration
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # HSTS (optional)
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     
#     # Include all the location blocks from the HTTP server block above
#     root /var/www/html/uptime-monitor;
#     index index.php index.html;
#     
#     # ... (copy all location blocks from above)
# }

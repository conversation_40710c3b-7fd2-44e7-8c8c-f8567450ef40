const { test, expect } = require('@playwright/test');

test.describe('API Endpoints', () => {
  
  test.beforeEach(async ({ page }) => {
    // Clear any existing sessions
    try {
      await page.goto('/logout.php');
    } catch (error) {
      // Ignore errors
    }
  });

  test.describe('Authentication API', () => {
    
    test('should handle login via API', async ({ page }) => {
      const response = await page.request.post('/api/auth.php', {
        data: {
          action: 'login',
          username: 'admin',
          password: 'admin'
        }
      });
      
      expect(response.status()).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.user.username).toBe('admin');
    });

    test('should reject invalid credentials via API', async ({ page }) => {
      const response = await page.request.post('/api/auth.php', {
        data: {
          action: 'login',
          username: 'admin',
          password: 'wrong'
        }
      });
      
      expect(response.status()).toBe(401);
      const data = await response.json();
      expect(data.success).toBe(false);
    });

    test('should check login status via API', async ({ page }) => {
      // First login
      await page.request.post('/api/auth.php', {
        data: {
          action: 'login',
          username: 'admin',
          password: 'admin'
        }
      });
      
      // Check status
      const response = await page.request.get('/api/auth.php');
      expect(response.status()).toBe(200);
      const data = await response.json();
      expect(data.logged_in).toBe(true);
    });

  });

  test.describe('Monitors API', () => {
    
    test.beforeEach(async ({ page }) => {
      // Login before each test
      await page.request.post('/api/auth.php', {
        data: {
          action: 'login',
          username: 'admin',
          password: 'admin'
        }
      });
    });

    test('should get monitors via API', async ({ page }) => {
      const response = await page.request.get('/api/monitors.php');
      expect(response.status()).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(Array.isArray(data.monitors)).toBe(true);
    });

    test('should add monitor via API', async ({ page }) => {
      const timestamp = Date.now();
      const response = await page.request.post('/api/monitors.php', {
        headers: {
          'Content-Type': 'application/json'
        },
        data: JSON.stringify({
          url: `https://api-test-${timestamp}.com`,
          email: `api-test-${timestamp}@example.com`
        })
      });

      const data = await response.json();
      expect(response.status()).toBe(200);
      expect(data.success).toBe(true);
    });

    test('should require authentication for monitors', async ({ page }) => {
      // Logout first
      await page.request.post('/api/auth.php', {
        data: { action: 'logout' }
      });
      
      const response = await page.request.get('/api/monitors.php');
      expect(response.status()).toBe(401);
    });

    test('should validate monitor data', async ({ page }) => {
      const response = await page.request.post('/api/monitors.php', {
        headers: {
          'Content-Type': 'application/json'
        },
        data: JSON.stringify({
          url: 'invalid-url',
          email: 'invalid-email'
        })
      });

      expect(response.status()).toBe(400);
      const data = await response.json();
      expect(data.success).toBe(false);
    });

  });

});

<!DOCTYPE html>
<html>
<head>
    <title>JavaScript API Test</title>
</head>
<body>
    <h2>JavaScript API Test</h2>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script>
        function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            fetch('api/check-status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url: 'https://google.com' })
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(data => {
                console.log('Response data:', data);
                resultDiv.innerHTML = '<pre>' + data + '</pre>';
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = 'Error: ' + error.message;
            });
        }
        
        // Test on page load
        window.onload = function() {
            console.log('Page loaded, testing API...');
            testAPI();
        };
    </script>
</body>
</html>

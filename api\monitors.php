<?php
/**
 * API endpoint for monitor management
 * Provides JSON API for frontend applications
 */

require_once '../config.php';

// Set JSON headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Require authentication for all monitor operations
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

switch ($method) {
    case 'GET':
        handleGetMonitors();
        break;
        
    case 'POST':
        handleAddMonitor($input);
        break;
        
    case 'PUT':
        handleUpdateMonitor($input);
        break;
        
    case 'DELETE':
        handleDeleteMonitor($input);
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
}

function handleGetMonitors() {
    $monitors = get_monitors();
    echo json_encode([
        'success' => true,
        'monitors' => $monitors,
        'count' => count($monitors)
    ]);
}

function handleAddMonitor($input) {
    if (!isset($input['url']) || !isset($input['email'])) {
        http_response_code(400);
        echo json_encode(['error' => 'URL and email required']);
        return;
    }
    
    $result = add_monitor($input['url'], $input['email']);
    
    if ($result['success']) {
        echo json_encode($result);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
}

function handleUpdateMonitor($input) {
    if (!isset($input['index'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Monitor index required']);
        return;
    }
    
    $monitors = get_monitors();
    $index = (int)$input['index'];
    
    if (!isset($monitors[$index])) {
        http_response_code(404);
        echo json_encode(['error' => 'Monitor not found']);
        return;
    }
    
    // Update monitor fields
    if (isset($input['status'])) {
        $monitors[$index]['status'] = $input['status'];
    }
    if (isset($input['url'])) {
        $monitors[$index]['url'] = $input['url'];
    }
    if (isset($input['email'])) {
        $monitors[$index]['email'] = $input['email'];
    }
    
    if (save_monitors($monitors)) {
        echo json_encode([
            'success' => true,
            'message' => 'Monitor updated successfully'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update monitor']);
    }
}

function handleDeleteMonitor($input) {
    if (!isset($input['index'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Monitor index required']);
        return;
    }
    
    $monitors = get_monitors();
    $index = (int)$input['index'];
    
    if (!isset($monitors[$index])) {
        http_response_code(404);
        echo json_encode(['error' => 'Monitor not found']);
        return;
    }
    
    // Remove monitor
    array_splice($monitors, $index, 1);
    
    if (save_monitors($monitors)) {
        echo json_encode([
            'success' => true,
            'message' => 'Monitor deleted successfully'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete monitor']);
    }
}
?>

<?php
require_once 'config.php';

$error_message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error_message = 'Please enter both username and password.';
    } elseif (authenticate_user($username, $password)) {
        // Successful login - redirect to dashboard
        header('Location: simple-dashboard.php');
        exit();
    } else {
        $error_message = 'Invalid username or password.';
    }
}

// If already logged in, redirect to dashboard
if (is_logged_in()) {
    header('Location: simple-dashboard.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Uptime Monitor - Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f9f9f9;
        }
        .container {
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background-color: white;
            border: 1px solid #ccc;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            box-sizing: border-box;
        }
        input[type="submit"] {
            width: 100%;
            padding: 10px;
            background-color: #007cba;
            color: white;
            border: none;
            cursor: pointer;
        }
        .error {
            background-color: #ffe6e6;
            color: #cc0000;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #cc0000;
        }
        .info {
            background-color: #e6f3ff;
            color: #0066cc;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Uptime Monitor</h1>
        
        <?php if ($error_message): ?>
            <div class="error"><?php echo htmlspecialchars($error_message); ?></div>
        <?php endif; ?>
        
        <div class="info">
            <strong>Default login:</strong><br>
            Username: admin<br>
            Password: admin
        </div>
        
        <form method="POST" action="">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required>
            
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
            
            <input type="submit" value="Login">
        </form>
    </div>
</body>
</html>

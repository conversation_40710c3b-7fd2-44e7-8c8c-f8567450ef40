<?php
/**
 * API endpoint for authentication
 * Supports both session-based and token-based auth for frontend compatibility
 */

require_once '../config.php';

// Set JSON headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

switch ($method) {
    case 'POST':
        if (isset($input['action'])) {
            switch ($input['action']) {
                case 'login':
                    handleLogin($input);
                    break;
                case 'logout':
                    handleLogout();
                    break;
                default:
                    http_response_code(400);
                    echo json_encode(['error' => 'Invalid action']);
            }
        } else {
            http_response_code(400);
            echo json_encode(['error' => 'Action required']);
        }
        break;
        
    case 'GET':
        handleStatus();
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
}

function handleLogin($input) {
    if (!isset($input['username']) || !isset($input['password'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Username and password required']);
        return;
    }
    
    $username = trim($input['username']);
    $password = $input['password'];
    
    if (authenticate_user($username, $password)) {
        echo json_encode([
            'success' => true,
            'message' => 'Login successful',
            'user' => [
                'username' => $username,
                'logged_in' => true
            ]
        ]);
    } else {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid credentials'
        ]);
    }
}

function handleLogout() {
    logout_user();
    echo json_encode([
        'success' => true,
        'message' => 'Logout successful'
    ]);
}

function handleStatus() {
    if (is_logged_in()) {
        echo json_encode([
            'logged_in' => true,
            'username' => $_SESSION['username'] ?? ''
        ]);
    } else {
        echo json_encode([
            'logged_in' => false
        ]);
    }
}
?>

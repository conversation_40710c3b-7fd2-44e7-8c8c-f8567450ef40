#!/bin/bash

# Uptime Monitor - Cron Setup Script
# This script helps set up the cron job for monitoring

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PHP_PATH="/usr/bin/php"
CRON_INTERVAL="*/5 * * * *"
LOG_FILE="/var/log/uptime-monitor-cron.log"

echo -e "${GREEN}Uptime Monitor - Cron Setup${NC}"
echo "=================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "Checking prerequisites..."

if ! command_exists php; then
    echo -e "${RED}Error: PHP is not installed or not in PATH${NC}"
    exit 1
fi

if ! command_exists crontab; then
    echo -e "${RED}Error: crontab is not available${NC}"
    exit 1
fi

# Get PHP path
PHP_PATH=$(which php)
echo -e "PHP found at: ${GREEN}$PHP_PATH${NC}"

# Check PHP version
PHP_VERSION=$(php -v | head -n 1 | cut -d ' ' -f 2)
echo -e "PHP version: ${GREEN}$PHP_VERSION${NC}"

# Check if script exists
MONITOR_SCRIPT="$SCRIPT_DIR/check-monitors.php"
if [ ! -f "$MONITOR_SCRIPT" ]; then
    echo -e "${RED}Error: check-monitors.php not found in $SCRIPT_DIR${NC}"
    exit 1
fi

echo -e "Monitor script: ${GREEN}$MONITOR_SCRIPT${NC}"

# Test script execution
echo "Testing script execution..."
if ! php "$MONITOR_SCRIPT" --help >/dev/null 2>&1; then
    echo -e "${RED}Error: Script execution failed${NC}"
    exit 1
fi

echo -e "${GREEN}Script test passed${NC}"

# Check current crontab
echo "Checking current crontab..."
CURRENT_CRON=$(crontab -l 2>/dev/null || echo "")

if echo "$CURRENT_CRON" | grep -q "check-monitors.php"; then
    echo -e "${YELLOW}Warning: Existing cron job found for check-monitors.php${NC}"
    echo "Current cron jobs:"
    echo "$CURRENT_CRON" | grep "check-monitors.php" || true
    echo ""
    read -p "Do you want to replace existing cron job? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Aborted."
        exit 0
    fi
fi

# Create cron job
echo "Setting up cron job..."

# Remove existing entries
NEW_CRON=$(echo "$CURRENT_CRON" | grep -v "check-monitors.php" || true)

# Add new cron job
CRON_COMMAND="$CRON_INTERVAL $PHP_PATH $MONITOR_SCRIPT >/dev/null 2>&1"
NEW_CRON="$NEW_CRON
$CRON_COMMAND"

# Install new crontab
echo "$NEW_CRON" | crontab -

echo -e "${GREEN}Cron job installed successfully!${NC}"
echo ""
echo "Cron job details:"
echo "  Schedule: $CRON_INTERVAL (every 5 minutes)"
echo "  Command: $PHP_PATH $MONITOR_SCRIPT"
echo "  Output: Redirected to /dev/null"
echo ""

# Test cron job
echo "Testing cron job..."
if php "$MONITOR_SCRIPT" --dry-run; then
    echo -e "${GREEN}Cron job test passed${NC}"
else
    echo -e "${RED}Cron job test failed${NC}"
    exit 1
fi

# Create log directory if needed
LOG_DIR="$SCRIPT_DIR/logs"
if [ ! -d "$LOG_DIR" ]; then
    mkdir -p "$LOG_DIR"
    chmod 755 "$LOG_DIR"
    echo -e "Created log directory: ${GREEN}$LOG_DIR${NC}"
fi

# Set permissions
echo "Setting permissions..."
chmod 755 "$MONITOR_SCRIPT"
chmod 755 "$SCRIPT_DIR"

if [ -w "$LOG_DIR" ]; then
    echo -e "Log directory is writable: ${GREEN}$LOG_DIR${NC}"
else
    echo -e "${YELLOW}Warning: Log directory may not be writable: $LOG_DIR${NC}"
fi

echo ""
echo -e "${GREEN}Setup completed successfully!${NC}"
echo ""
echo "Next steps:"
echo "1. Add monitors through the web interface"
echo "2. Wait 5 minutes for the first cron run"
echo "3. Check logs in: $LOG_DIR"
echo ""
echo "Useful commands:"
echo "  View cron jobs: crontab -l"
echo "  Remove cron job: crontab -e"
echo "  Test script: php $MONITOR_SCRIPT --dry-run"
echo "  View logs: tail -f $LOG_DIR/monitor-\$(date +%Y-%m-%d).log"
echo ""
echo "To uninstall the cron job, run:"
echo "  crontab -e"
echo "  # Remove the line containing 'check-monitors.php'"

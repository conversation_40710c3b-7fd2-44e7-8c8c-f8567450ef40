const { test, expect } = require('@playwright/test');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

test.describe.configure({ mode: 'serial' });

test.describe('Email Notifications System (Issue #4)', () => {

  const projectRoot = path.resolve(__dirname, '..');
  const testId = 'email-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  const monitorsFile = path.join(projectRoot, `monitors-${testId}.json`);
  const logsDir = path.join(projectRoot, `logs-${testId}`);
  const checkScript = path.join(projectRoot, 'check-monitors.php');
  
  // Helper function to execute PHP script
  const runPhpScript = (args = '') => {
    return new Promise((resolve, reject) => {
      const env = {
        ...process.env,
        EMAIL_TEST_MODE: 'true',
        MONITORS_FILE: monitorsFile,
        LOGS_DIR: logsDir
      };
      exec(`C:\\xampp\\php\\php.exe ${checkScript} ${args}`, {
        cwd: projectRoot,
        env: env
      }, (error, stdout, stderr) => {
        resolve({ error, stdout, stderr });
      });
    });
  };
  
  // Helper function to create test monitors
  const createTestMonitors = (monitors) => {
    fs.writeFileSync(monitorsFile, JSON.stringify(monitors, null, 2));
  };
  
  // Helper function to clean up test files
  const cleanup = () => {
    if (fs.existsSync(monitorsFile)) {
      fs.unlinkSync(monitorsFile);
    }
    if (fs.existsSync(logsDir)) {
      fs.rmSync(logsDir, { recursive: true, force: true });
    }
  };
  
  // Helper function to get email logs
  const getEmailLogs = () => {
    const today = new Date().toISOString().split('T')[0];
    const emailLogFile = path.join(logsDir, `emails-${today}.log`);
    if (fs.existsSync(emailLogFile)) {
      return fs.readFileSync(emailLogFile, 'utf8');
    }
    return '';
  };
  
  test.beforeEach(() => {
    cleanup();
    // Enable test mode for emails
    process.env.EMAIL_TEST_MODE = 'true';
  });
  
  test.afterEach(() => {
    cleanup();
    delete process.env.EMAIL_TEST_MODE;
  });

  test('should send email notification when site goes down', async () => {
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/500',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'active',
        last_status: 'up' // Previously up, now going down
      }
    ];
    
    createTestMonitors(testMonitors);
    
    const result = await runPhpScript('--verbose');
    
    expect(result.error).toBeNull();
    
    // Check that email was logged
    const emailLogs = getEmailLogs();
    expect(emailLogs).toContain('EMAIL SENT');
    expect(emailLogs).toContain('To: <EMAIL>');
    expect(emailLogs).toContain('Subject: 🚨 Website Down Alert: https://httpbin.org/status/500');
    expect(emailLogs).toContain('Website Down Alert');
    expect(emailLogs).toContain('Your monitored website is currently unavailable');
    
    // Check monitor logs
    const today = new Date().toISOString().split('T')[0];
    const logFile = path.join(logsDir, `monitor-${today}.log`);
    const logContent = fs.readFileSync(logFile, 'utf8');
    expect(logContent).toContain('Email notification sent for https://httpbin.org/status/500');
  });

  test('should not send duplicate emails for same incident', async () => {
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/500',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'active',
        last_status: 'down',
        incident_notified: true // Already notified
      }
    ];
    
    createTestMonitors(testMonitors);
    
    const result = await runPhpScript('--verbose');
    
    expect(result.error).toBeNull();
    
    // Check that no email was sent
    const emailLogs = getEmailLogs();
    expect(emailLogs).not.toContain('EMAIL SENT');
    
    // Check monitor logs for skip message
    const today = new Date().toISOString().split('T')[0];
    const logFile = path.join(logsDir, `monitor-${today}.log`);
    const logContent = fs.readFileSync(logFile, 'utf8');
    expect(logContent).toContain('Skipping email notification - already sent for this incident');
  });

  test('should include meaningful information in email', async () => {
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/404',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'active',
        last_status: 'up'
      }
    ];
    
    createTestMonitors(testMonitors);
    
    const result = await runPhpScript();
    
    expect(result.error).toBeNull();
    
    const emailLogs = getEmailLogs();
    
    // Check email contains required information
    expect(emailLogs).toContain('To: <EMAIL>');
    expect(emailLogs).toContain('https://httpbin.org/status/404');
    expect(emailLogs).toContain('Website URL:');
    expect(emailLogs).toContain('Detected at:');
    expect(emailLogs).toContain('Error:');
    expect(emailLogs).toContain('Response Time:');
    expect(emailLogs).toContain('HTTP 404');
    expect(emailLogs).toContain('What happens next?');
    expect(emailLogs).toContain('This alert will not be sent again for this incident');
  });

  test('should track incident state correctly', async () => {
    // First run - site is up
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'active'
      }
    ];
    
    createTestMonitors(testMonitors);
    await runPhpScript();
    
    // Check monitor was updated to 'up'
    let monitors = JSON.parse(fs.readFileSync(monitorsFile, 'utf8'));
    expect(monitors[0].last_status).toBe('up');
    expect(monitors[0]).not.toHaveProperty('incident_notified');
    
    // Second run - site goes down
    monitors[0].url = 'https://httpbin.org/status/500';
    createTestMonitors(monitors);
    await runPhpScript();
    
    // Check incident tracking
    monitors = JSON.parse(fs.readFileSync(monitorsFile, 'utf8'));
    expect(monitors[0].last_status).toBe('down');
    expect(monitors[0].incident_notified).toBe(true);
    expect(monitors[0]).toHaveProperty('incident_started');
    
    // Check email was sent
    const emailLogs = getEmailLogs();
    expect(emailLogs).toContain('EMAIL SENT');
  });

  test('should reset incident state when site comes back up', async () => {
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'active',
        last_status: 'down',
        incident_notified: true,
        incident_started: '2024-01-01 11:00:00'
      }
    ];
    
    createTestMonitors(testMonitors);
    
    const result = await runPhpScript();
    
    expect(result.error).toBeNull();
    
    // Check incident state was reset
    const monitors = JSON.parse(fs.readFileSync(monitorsFile, 'utf8'));
    expect(monitors[0].last_status).toBe('up');
    expect(monitors[0].incident_notified).toBe(false);
    expect(monitors[0]).toHaveProperty('incident_resolved');
  });

  test('should handle email sending failures gracefully', async () => {
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/500',
        email: 'invalid-email-format',
        added_at: '2024-01-01 12:00:00',
        status: 'active',
        last_status: 'up'
      }
    ];
    
    createTestMonitors(testMonitors);
    
    const result = await runPhpScript('--verbose');
    
    // Script should not crash even if email fails
    expect(result.error).toBeNull();
    expect(result.stdout).toContain('completed successfully');
    
    // Check error was logged
    const today = new Date().toISOString().split('T')[0];
    const logFile = path.join(logsDir, `monitor-${today}.log`);
    const logContent = fs.readFileSync(logFile, 'utf8');
    expect(logContent).toContain('FAILED');
  });

  test('should not send emails for successful checks', async () => {
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'active'
      }
    ];
    
    createTestMonitors(testMonitors);
    
    const result = await runPhpScript();
    
    expect(result.error).toBeNull();
    
    // No emails should be sent for successful checks
    const emailLogs = getEmailLogs();
    expect(emailLogs).toBe('');
  });

  test('should compose email with proper HTML formatting', async () => {
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/503',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'active',
        last_status: 'up'
      }
    ];
    
    createTestMonitors(testMonitors);
    
    const result = await runPhpScript();
    
    expect(result.error).toBeNull();
    
    const emailLogs = getEmailLogs();
    
    // Check HTML structure
    expect(emailLogs).toContain('<html>');
    expect(emailLogs).toContain('<body');
    expect(emailLogs).toContain('</html>');
    expect(emailLogs).toContain('font-family: Arial');
    expect(emailLogs).toContain('<table');
    expect(emailLogs).toContain('background-color:');
    expect(emailLogs).toContain('Content-Type: text/html; charset=UTF-8');
  });

});

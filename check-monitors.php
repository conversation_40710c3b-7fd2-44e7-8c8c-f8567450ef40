<?php
/**
 * Cron-compatible PHP script to check monitored URLs
 * 
 * This script should be run via cron every 5 minutes:
 * 0,5,10,15,20,25,30,35,40,45,50,55 * * * * /usr/bin/php /path/to/check-monitors.php
 * 
 * Features:
 * - Reads all monitored URLs from storage
 * - Makes HTTP requests to each URL
 * - Detects failures (connection errors, non-200 responses)
 * - Logs status and reports
 */

// Include configuration
require_once __DIR__ . '/config.php';

// Main monitoring function
function run_monitoring_check() {
    $start_time = microtime(true);
    
    log_monitor_event("Starting monitoring check cycle");
    
    // Get all monitors
    $monitors = get_monitors();
    
    if (empty($monitors)) {
        log_monitor_event("No monitors configured", 'INFO');
        return;
    }
    
    $total_monitors = count($monitors);
    $successful_checks = 0;
    $failed_checks = 0;
    
    log_monitor_event("Found {$total_monitors} monitors to check");
    
    // Check each monitor
    foreach ($monitors as $index => $monitor) {
        if ($monitor['status'] !== 'active') {
            log_monitor_event("Skipping inactive monitor: {$monitor['url']}", 'INFO');
            continue;
        }
        
        log_monitor_event("Checking URL: {$monitor['url']}");
        
        // Perform the check
        $check_result = check_url_status($monitor['url']);
        
        // Update monitor status
        update_monitor_status($index, $check_result);
        
        // Log the result
        if ($check_result['success']) {
            $successful_checks++;
            log_monitor_event(
                "✓ {$monitor['url']} - OK (HTTP {$check_result['http_code']}, {$check_result['response_time']}ms)",
                'INFO'
            );
        } else {
            $failed_checks++;
            $error_msg = $check_result['error'] ?: "HTTP {$check_result['http_code']}";
            log_monitor_event(
                "✗ {$monitor['url']} - FAILED ({$error_msg}, {$check_result['response_time']}ms)",
                'ERROR'
            );
            
            // TODO: Send email notification (Issue #4)
            // send_failure_notification($monitor, $check_result);
        }
        
        // Small delay between checks to be nice to servers
        usleep(500000); // 0.5 seconds
    }
    
    $total_time = round((microtime(true) - $start_time) * 1000, 2);
    
    log_monitor_event(
        "Monitoring cycle completed: {$successful_checks} successful, {$failed_checks} failed, {$total_time}ms total",
        'INFO'
    );
}

// Function to display usage information
function show_usage() {
    echo "Uptime Monitor - Cron Check Script\n";
    echo "Usage: php check-monitors.php [options]\n";
    echo "\nOptions:\n";
    echo "  --help, -h     Show this help message\n";
    echo "  --verbose, -v  Enable verbose output\n";
    echo "  --dry-run      Show what would be checked without actually checking\n";
    echo "\nExample cron entry (check every 5 minutes):\n";
    echo "*/5 * * * * /usr/bin/php " . __FILE__ . "\n";
}

// Function for dry run (testing purposes)
function dry_run() {
    echo "DRY RUN MODE - No actual checks will be performed\n\n";

    $monitors = get_monitors();

    if (empty($monitors)) {
        echo "No monitors configured.\n";
        return;
    }
    
    echo "Would check the following URLs:\n";
    foreach ($monitors as $index => $monitor) {
        $status = $monitor['status'] === 'active' ? 'ACTIVE' : 'INACTIVE';
        echo sprintf(
            "%d. %s [%s] -> %s\n",
            $index + 1,
            $monitor['url'],
            $status,
            $monitor['email']
        );
    }
    
    echo "\nTotal: " . count($monitors) . " monitors\n";
}

// Parse command line arguments
$options = getopt('hv', ['help', 'verbose', 'dry-run']);
$verbose = isset($options['v']) || isset($options['verbose']);

// Handle help option
if (isset($options['h']) || isset($options['help'])) {
    show_usage();
    exit(0);
}

// Handle dry run option
if (isset($options['dry-run'])) {
    dry_run();
    exit(0);
}

// Main execution
try {
    if ($verbose) {
        echo "Starting uptime monitoring check...\n";
    }
    
    run_monitoring_check();
    
    if ($verbose) {
        echo "Monitoring check completed successfully.\n";
        echo "Check logs in: " . LOGS_DIR . "/monitor-" . date('Y-m-d') . ".log\n";
    }
    
    exit(0);
    
} catch (Exception $e) {
    $error_msg = "Fatal error during monitoring check: " . $e->getMessage();
    log_monitor_event($error_msg, 'FATAL');
    
    if ($verbose) {
        echo "ERROR: {$error_msg}\n";
    }
    
    exit(1);
}
?>

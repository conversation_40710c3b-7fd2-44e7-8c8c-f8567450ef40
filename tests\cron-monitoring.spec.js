const { test, expect } = require('@playwright/test');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

test.describe.configure({ mode: 'serial' });

test.describe('Cron Monitoring System (Issue #3)', () => {

  const projectRoot = path.resolve(__dirname, '..');
  const monitorsFile = path.join(projectRoot, 'monitors.json');
  const logsDir = path.join(projectRoot, 'logs');
  const checkScript = path.join(projectRoot, 'check-monitors.php');

  // Helper function to execute PHP script
  const runPhpScript = (args = '') => {
    return new Promise((resolve, reject) => {
      exec(`C:\\xampp\\php\\php.exe ${checkScript} ${args}`, { cwd: projectRoot }, (error, stdout, stderr) => {
        resolve({ error, stdout, stderr });
      });
    });
  };
  
  // Helper function to create test monitors
  const createTestMonitors = (monitors) => {
    fs.writeFileSync(monitorsFile, JSON.stringify(monitors, null, 2));
  };
  
  // Helper function to clean up test files
  const cleanup = () => {
    if (fs.existsSync(monitorsFile)) {
      fs.unlinkSync(monitorsFile);
    }
    if (fs.existsSync(logsDir)) {
      fs.rmSync(logsDir, { recursive: true, force: true });
    }
  };
  
  test.beforeEach(() => {
    cleanup();
  });
  
  test.afterEach(() => {
    cleanup();
  });

  test('should exist and be executable', async () => {
    // Check if the script file exists
    expect(fs.existsSync(checkScript)).toBe(true);
    
    // Check if it's a PHP file
    const content = fs.readFileSync(checkScript, 'utf8');
    expect(content).toContain('<?php');
  });

  test('should show help when --help flag is used', async () => {
    const result = await runPhpScript('--help');
    
    expect(result.error).toBeNull();
    expect(result.stdout).toContain('Uptime Monitor - Cron Check Script');
    expect(result.stdout).toContain('Usage:');
    expect(result.stdout).toContain('--help');
    expect(result.stdout).toContain('--verbose');
    expect(result.stdout).toContain('--dry-run');
    expect(result.stdout).toContain('cron entry');
  });

  test('should handle empty monitors gracefully', async () => {
    // Create empty monitors file
    createTestMonitors([]);
    
    const result = await runPhpScript('--verbose');
    
    expect(result.error).toBeNull();
    expect(result.stdout).toContain('Starting uptime monitoring check');
    expect(result.stdout).toContain('completed successfully');
  });

  test('should perform dry run without making actual requests', async () => {
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'active'
      },
      {
        url: 'https://httpbin.org/status/404',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'inactive'
      }
    ];

    createTestMonitors(testMonitors);

    const result = await runPhpScript('--dry-run');

    expect(result.error).toBeNull();
    expect(result.stdout).toContain('DRY RUN MODE');
    expect(result.stdout).toContain('Would check the following URLs');
    expect(result.stdout).toContain('https://httpbin.org/status/200');
    expect(result.stdout).toContain('https://httpbin.org/status/404');
    expect(result.stdout).toContain('[ACTIVE]');
    expect(result.stdout).toContain('[INACTIVE]');
    expect(result.stdout).toContain('Total: 2 monitors');
  });

  test('should read monitors from storage', async () => {
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'active'
      }
    ];
    
    createTestMonitors(testMonitors);
    
    const result = await runPhpScript('--verbose');
    
    expect(result.error).toBeNull();
    expect(result.stdout).toContain('Starting uptime monitoring check');
    expect(result.stdout).toContain('completed successfully');
  });

  test('should create logs directory and log files', async () => {
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'active'
      }
    ];
    
    createTestMonitors(testMonitors);
    
    const result = await runPhpScript();
    
    expect(result.error).toBeNull();
    
    // Check if logs directory was created
    expect(fs.existsSync(logsDir)).toBe(true);
    
    // Check if log file was created
    const today = new Date().toISOString().split('T')[0];
    const logFile = path.join(logsDir, `monitor-${today}.log`);
    expect(fs.existsSync(logFile)).toBe(true);
    
    // Check log content
    const logContent = fs.readFileSync(logFile, 'utf8');
    expect(logContent).toContain('Starting monitoring check cycle');
    expect(logContent).toContain('Found 1 monitors to check');
    expect(logContent).toContain('Checking URL: https://httpbin.org/status/200');
  });

  test('should detect successful HTTP responses', async () => {
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'active'
      }
    ];
    
    createTestMonitors(testMonitors);
    
    const result = await runPhpScript();
    
    expect(result.error).toBeNull();
    
    // Check updated monitors file
    const updatedMonitors = JSON.parse(fs.readFileSync(monitorsFile, 'utf8'));
    expect(updatedMonitors[0]).toHaveProperty('last_check');
    expect(updatedMonitors[0]).toHaveProperty('last_status', 'up');
    expect(updatedMonitors[0]).toHaveProperty('last_response_time');
    expect(updatedMonitors[0]).toHaveProperty('last_http_code', 200);
    
    // Check log content
    const today = new Date().toISOString().split('T')[0];
    const logFile = path.join(logsDir, `monitor-${today}.log`);
    const logContent = fs.readFileSync(logFile, 'utf8');
    expect(logContent).toContain('✓ https://httpbin.org/status/200 - OK');
    expect(logContent).toContain('1 successful, 0 failed');
  });

  test('should detect HTTP failures', async () => {
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/500',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'active'
      }
    ];
    
    createTestMonitors(testMonitors);
    
    const result = await runPhpScript();
    
    expect(result.error).toBeNull();
    
    // Check updated monitors file
    const updatedMonitors = JSON.parse(fs.readFileSync(monitorsFile, 'utf8'));
    expect(updatedMonitors[0]).toHaveProperty('last_status', 'down');
    expect(updatedMonitors[0]).toHaveProperty('last_http_code', 500);
    
    // Check log content
    const today = new Date().toISOString().split('T')[0];
    const logFile = path.join(logsDir, `monitor-${today}.log`);
    const logContent = fs.readFileSync(logFile, 'utf8');
    expect(logContent).toContain('✗ https://httpbin.org/status/500 - FAILED');
    expect(logContent).toContain('0 successful, 1 failed');
  });

  test('should skip inactive monitors', async () => {
    const testMonitors = [
      {
        url: 'https://httpbin.org/status/200',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'inactive'
      }
    ];
    
    createTestMonitors(testMonitors);
    
    const result = await runPhpScript();
    
    expect(result.error).toBeNull();
    
    // Check that monitor wasn't updated (no last_check field)
    const updatedMonitors = JSON.parse(fs.readFileSync(monitorsFile, 'utf8'));
    expect(updatedMonitors[0]).not.toHaveProperty('last_check');
    
    // Check log content
    const today = new Date().toISOString().split('T')[0];
    const logFile = path.join(logsDir, `monitor-${today}.log`);
    const logContent = fs.readFileSync(logFile, 'utf8');
    expect(logContent).toContain('Skipping inactive monitor');
  });

  test('should handle connection errors gracefully', async () => {
    const testMonitors = [
      {
        url: 'https://this-domain-should-not-exist-12345.com',
        email: '<EMAIL>',
        added_at: '2024-01-01 12:00:00',
        status: 'active'
      }
    ];
    
    createTestMonitors(testMonitors);
    
    const result = await runPhpScript();
    
    expect(result.error).toBeNull();
    
    // Check updated monitors file
    const updatedMonitors = JSON.parse(fs.readFileSync(monitorsFile, 'utf8'));
    expect(updatedMonitors[0]).toHaveProperty('last_status', 'down');
    expect(updatedMonitors[0]).toHaveProperty('last_error');
    
    // Check log content
    const today = new Date().toISOString().split('T')[0];
    const logFile = path.join(logsDir, `monitor-${today}.log`);
    const logContent = fs.readFileSync(logFile, 'utf8');
    expect(logContent).toContain('✗');
    expect(logContent).toContain('FAILED');
  });

});

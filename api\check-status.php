<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['url']) || empty($input['url'])) {
    http_response_code(400);
    echo json_encode(['error' => 'URL is required']);
    exit;
}

$url = $input['url'];

// Validate URL
if (!filter_var($url, FILTER_VALIDATE_URL)) {
    echo json_encode([
        'status' => 'offline',
        'error' => 'Invalid URL format'
    ]);
    exit;
}

// Function to check URL status
function checkUrlStatus($url) {
    // First try HEAD request (faster)
    $result = makeRequest($url, true);

    // If HEAD request fails with method not allowed, try GET request
    if ($result['status'] === 'offline' &&
        (strpos($result['error'], 'Method not allowed') !== false ||
         strpos($result['error'], '405') !== false ||
         strpos($result['error'], 'HTTP 405') !== false)) {
        $result = makeRequest($url, false);
    }

    return $result;
}

function makeRequest($url, $headOnly = true) {
    // Try cURL first
    if (function_exists('curl_init')) {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Uptime Monitor Bot/1.0');

        if ($headOnly) {
            curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
            curl_setopt($ch, CURLOPT_HEADER, true);
        } else {
            curl_setopt($ch, CURLOPT_NOBODY, false); // GET request
            curl_setopt($ch, CURLOPT_HEADER, false);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
        }

        $start_time = microtime(true);
        $response = curl_exec($ch);
        $end_time = microtime(true);

        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        $response_time = round(($end_time - $start_time) * 1000);

        curl_close($ch);

        // If cURL worked, return result
        if (!$curl_error && $http_code > 0) {
            if ($http_code >= 200 && $http_code < 400) {
                return [
                    'status' => 'online',
                    'http_code' => $http_code,
                    'response_time' => $response_time,
                    'method' => $headOnly ? 'HEAD' : 'GET'
                ];
            } else {
                return [
                    'status' => 'offline',
                    'error' => "HTTP $http_code",
                    'response_time' => $response_time,
                    'method' => $headOnly ? 'HEAD' : 'GET'
                ];
            }
        }
    }

    // Fallback to file_get_contents for GET requests only
    if (!$headOnly && ini_get('allow_url_fopen')) {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10,
                'user_agent' => 'Uptime Monitor Bot/1.0',
                'ignore_errors' => true
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ]);

        $start_time = microtime(true);
        $response = @file_get_contents($url, false, $context);
        $end_time = microtime(true);
        $response_time = round(($end_time - $start_time) * 1000);

        if ($response !== false && isset($http_response_header)) {
            // Parse HTTP response code from headers
            $http_code = 200; // Default
            if (isset($http_response_header[0])) {
                preg_match('/HTTP\/\d\.\d\s+(\d+)/', $http_response_header[0], $matches);
                if (isset($matches[1])) {
                    $http_code = (int)$matches[1];
                }
            }

            if ($http_code >= 200 && $http_code < 400) {
                return [
                    'status' => 'online',
                    'http_code' => $http_code,
                    'response_time' => $response_time,
                    'method' => 'GET (fallback)'
                ];
            }
        }
    }

    // If everything fails
    return [
        'status' => 'offline',
        'error' => 'Unable to connect (cURL and file_get_contents failed)',
        'response_time' => 0,
        'method' => 'FAILED'
    ];
}

// Check the URL status
$result = checkUrlStatus($url);

// Add timestamp
$result['checked_at'] = date('Y-m-d H:i:s');

// Return JSON response
echo json_encode($result);
?>

<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['url']) || empty($input['url'])) {
    http_response_code(400);
    echo json_encode(['error' => 'URL is required']);
    exit;
}

$url = $input['url'];

// Validate URL
if (!filter_var($url, FILTER_VALIDATE_URL)) {
    echo json_encode([
        'status' => 'offline',
        'error' => 'Invalid URL format'
    ]);
    exit;
}

// Function to check URL status
function checkUrlStatus($url) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Uptime Monitor Bot/1.0');
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    $start_time = microtime(true);
    $response = curl_exec($ch);
    $end_time = microtime(true);
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    $response_time = round(($end_time - $start_time) * 1000); // Convert to milliseconds
    
    curl_close($ch);
    
    if ($curl_error) {
        return [
            'status' => 'offline',
            'error' => $curl_error,
            'response_time' => $response_time
        ];
    }
    
    // Consider 2xx and 3xx status codes as online
    if ($http_code >= 200 && $http_code < 400) {
        return [
            'status' => 'online',
            'http_code' => $http_code,
            'response_time' => $response_time
        ];
    } else {
        return [
            'status' => 'offline',
            'error' => "HTTP $http_code",
            'response_time' => $response_time
        ];
    }
}

// Check the URL status
$result = checkUrlStatus($url);

// Add timestamp
$result['checked_at'] = date('Y-m-d H:i:s');

// Return JSON response
echo json_encode($result);
?>

<?php
echo "<h2>API Test</h2>";

// Test the API directly
$url = 'http://localhost:8080/uptime-monitor/api/check-status.php';
$data = json_encode(['url' => 'https://google.com']);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($data)
]);

echo "Testing API with Google URL...<br>";
echo "Request URL: $url<br>";
echo "Request Data: $data<br><br>";

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $http_code<br>";
echo "cURL Error: " . ($error ? $error : "None") . "<br>";
echo "Response: <pre>" . htmlspecialchars($response) . "</pre>";

// Also test direct inclusion
echo "<hr><h3>Direct Test:</h3>";
$_SERVER['REQUEST_METHOD'] = 'POST';
$_POST = [];
file_put_contents('php://input', json_encode(['url' => 'https://google.com']));

ob_start();
include 'api/check-status.php';
$direct_result = ob_get_clean();

echo "Direct result: <pre>" . htmlspecialchars($direct_result) . "</pre>";
?>

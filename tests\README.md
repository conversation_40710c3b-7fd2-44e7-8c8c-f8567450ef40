# Test Documentation

## Autentimissüsteemi testid (Issue #1)

### Loodud testid Issue #1 jaoks:

1. **Login formi olemasolu test**
   - <PERSON><PERSON><PERSON><PERSON>, et login vorm on nähtav
   - Verifitseerib username ja password väljad
   - Kontrollib submit nupu olemasolu

2. **Vale sisselogimise test** 
   - Testib vale kasutajanime ja parooliga sisselogimist
   - Kontrollib, et kuvatakse veateade
   - Verifitseerib, et kasutaja jääb login lehele

3. **Õige sisselogimise test**
   - Testib õigete andmetega sisselogimist (admin/admin)
   - Kontrollib ümbersuunamist dashboard'ile
   - Verifitseerib, et dashboard sisu on nähtav

4. **Sessiooni säilimise test**
   - Kontrollib, et kasutaja jääb sisselogituks lehe värskendamisel
   - Testib sessiooni funktsionaalsust

5. **Ligipääsu kontroll test**
   - Kontrollib, et kaitstud lehtedele ei pääse ligi ilma sisselogimata
   - Verifitseerib ümbersuunamist login lehele
   - Testib, et monitor vorm on nähtav ainult sisselogitud kasutajatele

6. **Väljalogimine test**
   - Testib väljalogimine funktsionaalsust
   - Kontrollib ümbersuunamist login lehele

7. **Pärast väljalogimiset ligipääsu keelamine**
   - Verifitseerib, et pärast väljalogimiset ei saa kaitstud lehtedele ligi

8. **Vormide valideerimise test**
   - Testib tühjade väljadega esitamist
   - Kontrollib valideerimist

## Monitooringu konfiguratsiooni testid (Issue #2)

### Loodud testid Issue #2 jaoks:

1. **Monitor formi olemasolu test**
   - Kontrollib, et monitor vorm on nähtav sisselogitud kasutajale
   - Verifitseerib URL ja email väljad
   - Kontrollib submit nupu olemasolu

2. **URL valideerimise testid**
   - Testib vigaseid URL formaate
   - Kontrollib puuduvaid protokolle
   - Verifitseerib valideerimise sõnumeid

3. **Email valideerimise testid**
   - Testib vigaseid email formaate
   - Kontrollib puuduvaid @ märke
   - Verifitseerib valideerimise sõnumeid

4. **Nõutavate väljade test**
   - Testib tühje URL ja email välju
   - Kontrollib, et mõlemad väljad on nõutavad

5. **Edukas monitor sissekanne**
   - Testib õigete andmetega sisestamist
   - Kontrollib eduteadete kuvamist

6. **Duplikaatide ennetamise test**
   - Testib sama URL ja email kombinatsiooni korduvat lisamist
   - Kontrollib duplikaadi vea sõnumit

7. **Olemasolevate sissekannet kuvamise test**
   - Kontrollib, et lisatud monitorid kuvatakse
   - Testib andmete püsivust lehe värskendamisel

8. **Erinevate URL formaatide test**
   - Testib erinevaid kehtivaid URL formaate
   - Kontrollib http/https, portide, alamdomeenide toetust

9. **Erinevate email formaatide test**
   - Testib erinevaid kehtivaid email formaate
   - Kontrollib täppide, plussmärkide, alamdomeenide toetust

## Cron monitooringu testid (Issue #3)

### Loodud testid Issue #3 jaoks:

1. **Skripti olemasolu ja käivitatavuse test**
   - Kontrollib, et check-monitors.php fail eksisteerib
   - Verifitseerib, et see on kehtiv PHP fail

2. **Abi sõnumi test**
   - Testib --help lippu
   - Kontrollib kasutamisjuhendi kuvamist
   - Verifitseerib kõigi valikute olemasolu

3. **Tühjade monitoride käsitlemise test**
   - Testib skripti käitumist, kui monitoreid pole
   - Kontrollib graatsilist käsitlemist

4. **Kuiva jooksu (dry-run) test**
   - Testib --dry-run lippu
   - Kontrollib, et tegelikke päringuid ei tehta
   - Verifitseerib monitoride nimekirja kuvamist

5. **Monitoride lugemise test**
   - Testib monitoride lugemist monitors.json failist
   - Kontrollib skripti põhifunktsionaalsust

6. **Logimise funktsionaalsuse test**
   - Kontrollib logs/ kausta loomist
   - Verifitseerib logifailide genereerimist
   - Testib logi sisu õigsust

7. **Edukate HTTP vastuste tuvastamise test**
   - Testib 200 HTTP koodi tuvastamist
   - Kontrollib monitoori staatuse uuendamist
   - Verifitseerib edulogide kirjutamist

8. **HTTP vigade tuvastamise test**
   - Testib 500 HTTP koodi käsitlemist
   - Kontrollib ebaõnnestumiste logimist
   - Verifitseerib vea staatuse salvestamist

9. **Mitteaktiivsete monitoride vahelejätmise test**
   - Testib inactive staatusega monitoride ignoreerimist
   - Kontrollib, et neid ei kontrollita

10. **Ühendusevigade käsitlemise test**
    - Testib DNS vigade ja ühendusprobleemide käsitlemist
    - Kontrollib graatsilist veakäsitlust

## API testid (Frontend ühilduvus)

### Loodud testid API endpointide jaoks:

1. **Autentimise API testid**
   - Testib JSON-põhist sisselogimist
   - Kontrollib API vastuste formaati
   - Verifitseerib CORS headereid

2. **Monitoride API testid**
   - Testib REST API funktsionaalsust
   - Kontrollib JSON andmete vahetust
   - Verifitseerib autentimise nõudeid

3. **API ja PHP ühilduvuse testid**
   - Kontrollib, et mõlemad süsteemid töötavad paralleelselt
   - Testib andmete sünkroniseerimist

## Email teavituste testid (Issue #4)

### Loodud testid Issue #4 jaoks:

1. **Email saatmise test**
   - Testib email teavituse saatmist, kui sait läheb alla
   - Kontrollib email logi faili loomist
   - Verifitseerib email sisu ja formaati

2. **Duplikaatide vältimise test**
   - Testib, et sama incident kohta saadetakse ainult üks email
   - Kontrollib incident_notified lippu
   - Verifitseerib logi sõnumeid

3. **Email sisu test**
   - Kontrollib, et email sisaldab vajalikku infot
   - Testib URL-i, ajatemplit, vea kirjeldust
   - Verifitseerib HTML formaati

4. **Incident state tracking test**
   - Testib incident oleku jälgimist
   - Kontrollib oleku muutumist (up -> down -> up)
   - Verifitseerib incident_started ja incident_resolved välju

5. **Incident taastamise test**
   - Testib, et incident olek nullitakse, kui sait tuleb tagasi
   - Kontrollib incident_notified lipu lähtestamist
   - Verifitseerib incident_resolved ajatemplit

6. **Email vigade käsitlemise test**
   - Testib graatsilist käsitlemist, kui email saatmine ebaõnnestub
   - Kontrollib, et skript ei katkesta töötamist
   - Verifitseerib vea logimist

7. **Edukate kontrollide test**
   - Testib, et edukate kontrollide korral emaile ei saadeta
   - Kontrollib email logide puudumist
   - Verifitseerib ainult monitooringu logisid

8. **HTML email formaadi test**
   - Testib email HTML struktuuri
   - Kontrollib CSS stiilide olemasolu
   - Verifitseerib Content-Type headerit

## Testide käivitamine:

```bash
# Kõik testid
npm test

# Testid koos brauseri aknaga
npm run test:headed

# Testid UI-ga
npm run test:ui

# Ainult autentimise testid
npx playwright test auth.spec.js

# Ainult monitooringu testid
npx playwright test monitor-config.spec.js

# Ainult cron monitooringu testid
npx playwright test cron-monitoring.spec.js

# Ainult API testid
npx playwright test api.spec.js

# Ainult email teavituste testid
npx playwright test email-notifications.spec.js
```

## Eeldused testide käivitamiseks:

- PHP server peab töötama localhost:8000 pordil
- Rakenduse failid peavad olema projekt kaustas
- Vaikimisi kasutajanimi/parool: admin/admin
- Dashboard.php peab sisaldama monitooringu vormi

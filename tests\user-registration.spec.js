const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

test.describe('User Registration System', () => {

  const projectRoot = path.resolve(__dirname, '..');
  const testId = 'reg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  const testDbFile = path.join(projectRoot, `test_uptime_monitor_${testId}.db`);

  // Helper function to clean up test files
  const cleanup = () => {
    if (fs.existsSync(testDbFile)) {
      fs.unlinkSync(testDbFile);
    }
  };

  test.beforeEach(async ({ page }) => {
    cleanup();
    
    // Set environment variable for test database
    await page.addInitScript((dbFile) => {
      window.TEST_DATABASE_FILE = dbFile;
    }, testDbFile);
    
    // Clear any existing sessions
    try {
      await page.goto('/logout.php');
    } catch (error) {
      // Ignore errors
    }
  });

  test.afterEach(() => {
    cleanup();
  });

  test.describe('Registration Page', () => {

    test('should display registration form', async ({ page }) => {
      await page.goto('/register.php');
      
      // Check page title
      await expect(page).toHaveTitle(/Register.*Uptime Monitor/);
      
      // Check form elements
      await expect(page.locator('h1')).toContainText('Register');
      await expect(page.locator('input[name="username"]')).toBeVisible();
      await expect(page.locator('input[name="email"]')).toBeVisible();
      await expect(page.locator('input[name="password"]')).toBeVisible();
      await expect(page.locator('input[name="confirm_password"]')).toBeVisible();
      await expect(page.locator('input[type="submit"]')).toBeVisible();
      
      // Check labels
      await expect(page.locator('label[for="username"]')).toContainText('Username');
      await expect(page.locator('label[for="email"]')).toContainText('Email');
      await expect(page.locator('label[for="password"]')).toContainText('Password');
      await expect(page.locator('label[for="confirm_password"]')).toContainText('Confirm Password');
    });

    test('should have login link', async ({ page }) => {
      await page.goto('/register.php');
      
      const loginLink = page.locator('a[href="index.php"]');
      await expect(loginLink).toBeVisible();
      await expect(loginLink).toContainText('Login here');
    });

    test('should show password requirements', async ({ page }) => {
      await page.goto('/register.php');
      
      await expect(page.locator('.password-requirements')).toContainText('at least 6 characters');
    });

  });

  test.describe('Registration Validation', () => {

    test('should require all fields', async ({ page }) => {
      await page.goto('/register.php');
      
      // Try to submit empty form
      await page.click('input[type="submit"]');
      
      // HTML5 validation should prevent submission
      const usernameField = page.locator('input[name="username"]');
      await expect(usernameField).toHaveAttribute('required');
    });

    test('should validate password confirmation', async ({ page }) => {
      await page.goto('/register.php');
      
      await page.fill('input[name="username"]', 'testuser');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'password123');
      await page.fill('input[name="confirm_password"]', 'different123');
      
      await page.click('input[type="submit"]');
      
      // Should show error message
      await expect(page.locator('.error')).toContainText('Passwords do not match');
    });

    test('should validate username length with HTML5', async ({ page }) => {
      await page.goto('/register.php');

      // Check HTML5 validation attributes
      const usernameField = page.locator('input[name="username"]');
      await expect(usernameField).toHaveAttribute('minlength', '3');
      await expect(usernameField).toHaveAttribute('maxlength', '50');

      // Try to fill with short username
      await page.fill('input[name="username"]', 'ab'); // Too short
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'password123');
      await page.fill('input[name="confirm_password"]', 'password123');

      // HTML5 validation should prevent form submission
      await page.click('input[type="submit"]');

      // Should still be on registration page due to HTML5 validation
      await expect(page).toHaveURL(/register\.php/);
    });

    test('should validate email format with HTML5', async ({ page }) => {
      await page.goto('/register.php');

      // Check HTML5 validation attributes
      const emailField = page.locator('input[name="email"]');
      await expect(emailField).toHaveAttribute('type', 'email');

      await page.fill('input[name="username"]', 'testuser');
      await page.fill('input[name="email"]', 'invalid-email');
      await page.fill('input[name="password"]', 'password123');
      await page.fill('input[name="confirm_password"]', 'password123');

      // HTML5 validation should prevent form submission
      await page.click('input[type="submit"]');

      // Should still be on registration page due to HTML5 validation
      await expect(page).toHaveURL(/register\.php/);
    });

    test('should validate password length with HTML5', async ({ page }) => {
      await page.goto('/register.php');

      // Check HTML5 validation attributes
      const passwordField = page.locator('input[name="password"]');
      await expect(passwordField).toHaveAttribute('minlength', '6');

      await page.fill('input[name="username"]', 'testuser');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', '123'); // Too short
      await page.fill('input[name="confirm_password"]', '123');

      // HTML5 validation should prevent form submission
      await page.click('input[type="submit"]');

      // Should still be on registration page due to HTML5 validation
      await expect(page).toHaveURL(/register\.php/);
    });

  });

  test.describe('Successful Registration', () => {

    test('should register new user successfully', async ({ page }) => {
      await page.goto('/register.php');
      
      const timestamp = Date.now();
      await page.fill('input[name="username"]', `testuser${timestamp}`);
      await page.fill('input[name="email"]', `test${timestamp}@example.com`);
      await page.fill('input[name="password"]', 'password123');
      await page.fill('input[name="confirm_password"]', 'password123');
      
      await page.click('input[type="submit"]');
      
      // Should show success message
      await expect(page.locator('.success')).toContainText('registered successfully');
      await expect(page.locator('.success')).toContainText('You can now login');
      
      // Form should be cleared
      await expect(page.locator('input[name="username"]')).toHaveValue('');
      await expect(page.locator('input[name="email"]')).toHaveValue('');
    });

    test('should prevent duplicate usernames', async ({ page }) => {
      await page.goto('/register.php');

      const timestamp = Date.now();
      const username = `testuser${timestamp}`;

      // Register first user
      await page.fill('input[name="username"]', username);
      await page.fill('input[name="email"]', `test1${timestamp}@example.com`);
      await page.fill('input[name="password"]', 'password123');
      await page.fill('input[name="confirm_password"]', 'password123');
      await page.click('input[type="submit"]');

      // Wait for success message or check if already exists
      try {
        await expect(page.locator('.success')).toBeVisible({ timeout: 2000 });
      } catch (e) {
        // User might already exist, continue with test
      }

      // Try to register with same username
      await page.fill('input[name="username"]', username);
      await page.fill('input[name="email"]', `test2${timestamp}@example.com`);
      await page.fill('input[name="password"]', 'password123');
      await page.fill('input[name="confirm_password"]', 'password123');
      await page.click('input[type="submit"]');

      // Should show error message
      await expect(page.locator('.error')).toContainText('already exists');
    });

    test('should prevent duplicate emails', async ({ page }) => {
      await page.goto('/register.php');

      const timestamp = Date.now();
      const email = `test${timestamp}@example.com`;

      // Register first user
      await page.fill('input[name="username"]', `testuser1${timestamp}`);
      await page.fill('input[name="email"]', email);
      await page.fill('input[name="password"]', 'password123');
      await page.fill('input[name="confirm_password"]', 'password123');
      await page.click('input[type="submit"]');

      // Wait for success message or check if already exists
      try {
        await expect(page.locator('.success')).toBeVisible({ timeout: 2000 });
      } catch (e) {
        // Email might already exist, continue with test
      }

      // Try to register with same email
      await page.fill('input[name="username"]', `testuser2${timestamp}`);
      await page.fill('input[name="email"]', email);
      await page.fill('input[name="password"]', 'password123');
      await page.fill('input[name="confirm_password"]', 'password123');
      await page.click('input[type="submit"]');

      // Should show error message
      await expect(page.locator('.error')).toContainText('already exists');
    });

  });

  test.describe('Integration with Login', () => {

    test('should redirect to dashboard if already logged in', async ({ page }) => {
      // Login first
      await page.goto('/');
      await page.fill('input[name="username"]', 'admin');
      await page.fill('input[name="password"]', 'admin');
      await page.click('input[type="submit"]');
      
      // Try to access register page
      await page.goto('/register.php');
      
      // Should be redirected to dashboard
      await expect(page).toHaveURL(/dashboard\.php/);
    });

    test('should be able to login after registration', async ({ page }) => {
      // Register new user
      await page.goto('/register.php');
      
      const timestamp = Date.now();
      const username = `testuser${timestamp}`;
      const password = 'password123';
      
      await page.fill('input[name="username"]', username);
      await page.fill('input[name="email"]', `test${timestamp}@example.com`);
      await page.fill('input[name="password"]', password);
      await page.fill('input[name="confirm_password"]', password);
      await page.click('input[type="submit"]');
      
      // Wait for success message
      await expect(page.locator('.success')).toBeVisible();
      
      // Go to login page
      await page.click('a[href="index.php"]');
      
      // Login with new credentials
      await page.fill('input[name="username"]', username);
      await page.fill('input[name="password"]', password);
      await page.click('input[type="submit"]');
      
      // Should be redirected to dashboard
      await expect(page).toHaveURL(/dashboard\.php/);
      await expect(page.locator('h1')).toContainText('Dashboard');
    });

  });

  test.describe('UI and Accessibility', () => {

    test('should have proper form labels', async ({ page }) => {
      await page.goto('/register.php');
      
      // Check that all inputs have associated labels
      await expect(page.locator('label[for="username"]')).toBeVisible();
      await expect(page.locator('label[for="email"]')).toBeVisible();
      await expect(page.locator('label[for="password"]')).toBeVisible();
      await expect(page.locator('label[for="confirm_password"]')).toBeVisible();
    });

    test('should have proper input types', async ({ page }) => {
      await page.goto('/register.php');
      
      await expect(page.locator('input[name="username"]')).toHaveAttribute('type', 'text');
      await expect(page.locator('input[name="email"]')).toHaveAttribute('type', 'email');
      await expect(page.locator('input[name="password"]')).toHaveAttribute('type', 'password');
      await expect(page.locator('input[name="confirm_password"]')).toHaveAttribute('type', 'password');
    });

    test('should have proper validation attributes', async ({ page }) => {
      await page.goto('/register.php');
      
      // Check required attributes
      await expect(page.locator('input[name="username"]')).toHaveAttribute('required');
      await expect(page.locator('input[name="email"]')).toHaveAttribute('required');
      await expect(page.locator('input[name="password"]')).toHaveAttribute('required');
      await expect(page.locator('input[name="confirm_password"]')).toHaveAttribute('required');
      
      // Check length constraints
      await expect(page.locator('input[name="username"]')).toHaveAttribute('minlength', '3');
      await expect(page.locator('input[name="username"]')).toHaveAttribute('maxlength', '50');
      await expect(page.locator('input[name="password"]')).toHaveAttribute('minlength', '6');
      await expect(page.locator('input[name="confirm_password"]')).toHaveAttribute('minlength', '6');
    });

  });

});
